name: Build ESP32 Project with LVGL

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        submodules: 'recursive'
    
    - name: Setup ESP-IDF
      uses: espressif/esp-idf-ci-action@v1
      with:
        esp_idf_version: v5.4.1
        target: esp32s3
    
    - name: Install dependencies
      run: |
        # ESP-IDF component manager will automatically download LVGL 8.3.10
        idf.py reconfigure
    
    - name: Build project
      run: |
        idf.py build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          build/*.bin
          build/*.elf
          build/*.map
        retention-days: 30

  check-dependencies:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Setup ESP-IDF
      uses: espressif/esp-idf-ci-action@v1
      with:
        esp_idf_version: v5.4.1
        target: esp32s3
    
    - name: Check LVGL version
      run: |
        echo "Checking LVGL dependency..."
        idf.py dependency-graph
        
    - name: Validate component manifest
      run: |
        # Validate the idf_component.yml file
        python -c "
        import yaml
        with open('idf_component.yml', 'r') as f:
            config = yaml.safe_load(f)
            lvgl_version = config['dependencies']['lvgl/lvgl']['version']
            print(f'LVGL version configured: {lvgl_version}')
            assert lvgl_version == '8.3.10', f'Expected LVGL 8.3.10, got {lvgl_version}'
        "
