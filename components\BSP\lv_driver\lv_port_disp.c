/**
 * @file lv_port_disp_templ.c
 *
 */

/*Copy this file as "lv_port_disp.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_disp.h"
#include "../RGBLCD/ltdc.h"
#include <stdbool.h>
#include <string.h>
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

/*********************
 *      DEFINES
 *********************/
#ifndef MY_DISP_HOR_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen width, default value 320 is used for now.
    #define MY_DISP_HOR_RES    800
#endif

#ifndef MY_DISP_VER_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen height, default value 240 is used for now.
    #define MY_DISP_VER_RES    480
#endif

static const char *TAG = "LVGL_DISP";

/* 临界区保护 */
static portMUX_TYPE my_spinlock = portMUX_INITIALIZER_UNLOCKED;

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void disp_init(void);

static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);
static void vsync_task(void *pvParameters);
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//        const lv_area_t * fill_area, lv_color_t color);

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_disp_init(void)
{
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();

    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /**
     * LVGL requires a buffer where it internally draws the widgets.
     * Later this buffer will passed to your display driver's `flush_cb` to copy its content to your display.
     * The buffer has to be greater than 1 display row
     *
     * There are 3 buffering configurations:
     * 1. Create ONE buffer:
     *      LVGL will draw the display's content here and writes it to your display
     *
     * 2. Create TWO buffer:
     *      LVGL will draw the display's content to a buffer and writes it your display.
     *      You should use DMA to write the buffer's content to the display.
     *      It will enable LVGL to draw the next part of the screen to the other buffer while
     *      the data is being sent form the first buffer. It makes rendering and flushing parallel.
     *
     * 3. Double buffering
     *      Set 2 screens sized buffers and set disp_drv.full_refresh = 1.
     *      This way LVGL will always provide the whole rendered screen in `flush_cb`
     *      and you only need to change the frame buffer's address.
     */

    /* Example for 3) - Full screen double buffering for tear-free display */
    static lv_disp_draw_buf_t draw_buf_dsc_3;
    /* 使用PSRAM分配全屏缓冲区以避免内存不足 */
    static lv_color_t *buf_3_1;
    static lv_color_t *buf_3_2;

    /* 分配全屏缓冲区 */
    buf_3_1 = heap_caps_malloc(MY_DISP_HOR_RES * MY_DISP_VER_RES * sizeof(lv_color_t),
                               MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    buf_3_2 = heap_caps_malloc(MY_DISP_HOR_RES * MY_DISP_VER_RES * sizeof(lv_color_t),
                               MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);

    if (buf_3_1 && buf_3_2) {
        lv_disp_draw_buf_init(&draw_buf_dsc_3, buf_3_1, buf_3_2,
                              MY_DISP_HOR_RES * MY_DISP_VER_RES);
        ESP_LOGI(TAG, "Full screen double buffering enabled");
    } else {
        ESP_LOGE(TAG, "Failed to allocate full screen buffers, falling back to partial buffering");
        /* 回退到部分缓冲 */
        static lv_color_t buf_fallback_1[MY_DISP_HOR_RES * 60];
        static lv_color_t buf_fallback_2[MY_DISP_HOR_RES * 60];
        lv_disp_draw_buf_init(&draw_buf_dsc_3, buf_fallback_1, buf_fallback_2, MY_DISP_HOR_RES * 60);
    }

    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/

    static lv_disp_drv_t disp_drv;                         /*Descriptor of a display driver*/
    lv_disp_drv_init(&disp_drv);                    /*Basic initialization*/

    /*Set up the functions to access to your display*/

    /*Set the resolution of the display*/
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;

    /*Used to copy the buffer's content to the display*/
    disp_drv.flush_cb = disp_flush;

    /*Set a display buffer*/
    disp_drv.draw_buf = &draw_buf_dsc_3;

    /*Required for Example 3) - Enable full refresh for tear-free display*/
    disp_drv.full_refresh = 1;

    /* Fill a memory array with a color if you have GPU.
     * Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
     * But if you have a different GPU you can use with this callback.*/
    //disp_drv.gpu_fill_cb = gpu_fill;

    /*Finally register the driver*/
    lv_disp_drv_register(&disp_drv);

    /* 启动垂直同步任务 */
    xTaskCreate(vsync_task, "vsync_task", 1024, NULL, 5, NULL);
    ESP_LOGI(TAG, "VSync task started for tear-free display");
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*Initialize your display and the required peripherals.*/
static void disp_init(void)
{
    
    ltdc_init();
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void)
{
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void)
{
    disp_flush_enabled = false;
}

/* 垂直同步等待标志 */
static volatile bool vsync_ready = true;

/**
 * @brief       垂直同步模拟任务
 * @param       pvParameters: 任务参数
 * @retval      无
 */
static void vsync_task(void *pvParameters)
{
    const TickType_t vsync_period = pdMS_TO_TICKS(16); /* 60Hz垂直同步 */
    TickType_t last_wake_time = xTaskGetTickCount();

    while (1) {
        vTaskDelayUntil(&last_wake_time, vsync_period);
        vsync_ready = true;
    }
}

/*Flush the content of the internal buffer the specific area on the display
 *Optimized for full screen double buffering with DMA and tear-free display*/
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    if(disp_flush_enabled) {
        int32_t w = area->x2 - area->x1 + 1;
        int32_t h = area->y2 - area->y1 + 1;
        int32_t total_pixels = w * h;

        /* 检查是否为全屏刷新 */
        bool is_full_screen = (area->x1 == 0 && area->y1 == 0 &&
                              w == MY_DISP_HOR_RES && h == MY_DISP_VER_RES);

        if (is_full_screen) {
            /* 全屏刷新 - 使用DMA批量传输，防止撕裂 */
            ESP_LOGD(TAG, "Full screen refresh");

            /* 等待垂直同步（模拟） */
            while (!vsync_ready) {
                vTaskDelay(pdMS_TO_TICKS(1));
            }
            vsync_ready = false;

            /* 使用DMA批量传输整个屏幕 */
            taskENTER_CRITICAL(&my_spinlock);
            esp_lcd_panel_draw_bitmap(panel_handle, 0, 0, MY_DISP_HOR_RES, MY_DISP_VER_RES, color_p);
            taskEXIT_CRITICAL(&my_spinlock);

            vsync_ready = true;
        } else {
            /* 部分刷新 - 优化处理 */
            /* Check if it's a single color fill */
            bool is_single_color = true;
            lv_color_t first_color = color_p[0];

            if(total_pixels > 64) {
                /* Sample check for performance */
                for(int i = 16; i < total_pixels; i += 16) {
                    if(color_p[i].full != first_color.full) {
                        is_single_color = false;
                        break;
                    }
                }
            } else {
                is_single_color = false;
            }

            if(is_single_color && total_pixels > 64) {
                /* Use color fill for single-color areas */
                uint16_t color_565 = first_color.full;
                ltdc_color_fill(area->x1, area->y1, area->x2, area->y2, color_565);
            } else {
                /* Use optimized batch transfer for mixed colors */
                ltdc_draw_bitmap_fast(area->x1, area->y1, w, h, (uint16_t*)color_p);
            }
        }
    }

    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    lv_disp_flush_ready(disp_drv);
}

/*OPTIONAL: GPU INTERFACE*/

/*If your MCU has hardware accelerator (GPU) then you can use it to fill a memory with a color*/
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//                    const lv_area_t * fill_area, lv_color_t color)
//{
//    /*It's an example code which should be done by your GPU*/
//    int32_t x, y;
//    dest_buf += dest_width * fill_area->y1; /*Go to the first line*/
//
//    for(y = fill_area->y1; y <= fill_area->y2; y++) {
//        for(x = fill_area->x1; x <= fill_area->x2; x++) {
//            dest_buf[x] = color;
//        }
//        dest_buf+=dest_width;    /*Go to the next line*/
//    }
//}


#else /*Enable this file at the top*/

/*This dummy typedef exists purely to silence -Wpedantic.*/
typedef int keep_pedantic_happy;
#endif
