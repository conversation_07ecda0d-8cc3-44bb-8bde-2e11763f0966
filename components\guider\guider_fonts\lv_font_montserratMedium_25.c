/*
 * Copyright 2025 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */
/*******************************************************************************
 * Size: 25 px
 * Bpp: 4
 * Opts: --user-data-dir=C:\Users\<USER>\AppData\Roaming\gui-guider --app-path=D:\GUI_Guider\Gui-Guider\resources\app.asar --no-sandbox --no-zygote --lang=zh-CN --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1755411396113839 --launch-time-ticks=857559356 --mojo-platform-channel-handle=2860 --field-trial-handle=1716,i,5431673224908323737,3035983149899318021,131072 --disable-features=SpareRendererForSitePerProcess,WinRetrieveSuggestionsOnlyOnDemand /prefetch:1
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_25
#define LV_FONT_MONTSERRATMEDIUM_25 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_25

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0xfc, 0xf, 0xfb, 0xf, 0xfa, 0xe, 0xfa,
    0xe, 0xf9, 0xd, 0xf9, 0xd, 0xf8, 0xc, 0xf7,
    0xb, 0xf7, 0xb, 0xf6, 0xa, 0xf6, 0xa, 0xf5,
    0x5, 0x82, 0x0, 0x0, 0x1, 0x40, 0x1e, 0xfb,
    0x3f, 0xfe, 0xb, 0xf6,

    /* U+0022 "\"" */
    0x6f, 0xa0, 0xe, 0xf3, 0x6f, 0xa0, 0xe, 0xf2,
    0x5f, 0x90, 0xd, 0xf2, 0x5f, 0x90, 0xd, 0xf1,
    0x5f, 0x80, 0xc, 0xf1, 0x5f, 0x80, 0xc, 0xf1,
    0x4f, 0x80, 0xc, 0xf0, 0x1, 0x0, 0x0, 0x10,

    /* U+0023 "#" */
    0x0, 0x0, 0x5, 0xf7, 0x0, 0x0, 0xde, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x50, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x0, 0x9, 0xf2, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x4f,
    0x80, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x7e, 0xee, 0xff, 0xee, 0xee,
    0xff, 0xee, 0xed, 0x0, 0x0, 0x1f, 0xa0, 0x0,
    0x9, 0xf2, 0x0, 0x0, 0x0, 0x3, 0xf8, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x60,
    0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xf5,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x30, 0x0, 0x1f, 0xb0, 0x0, 0x4, 0xee, 0xef,
    0xfe, 0xee, 0xee, 0xff, 0xee, 0xe1, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xe, 0xd0, 0x0, 0x6, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0xa, 0xf1, 0x0, 0x0,
    0x0, 0x4, 0xf8, 0x0, 0x0, 0xcf, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x60, 0x0, 0xd, 0xe0, 0x0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x75, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbe, 0xff,
    0xfc, 0x82, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xc, 0xff, 0xa5, 0xec, 0x58, 0xdf,
    0x60, 0x4f, 0xf7, 0x0, 0xeb, 0x0, 0x5, 0x0,
    0x7f, 0xf0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x7f,
    0xf0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0x4f, 0xfa,
    0x0, 0xeb, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xb7, 0x10, 0x0, 0x0, 0x3, 0x8d, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xed, 0xbf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xeb, 0x2, 0xcf, 0xf3,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0xf, 0xf7, 0x5b, 0x20,
    0x0, 0xeb, 0x0, 0x7f, 0xf3, 0xcf, 0xfa, 0x64,
    0xec, 0x5a, 0xff, 0xc0, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x49, 0xdf, 0xff, 0xeb,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xeb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xc,
    0xf2, 0x0, 0x0, 0x2f, 0xf9, 0x9e, 0xe2, 0x0,
    0x0, 0x7, 0xf7, 0x0, 0x0, 0xa, 0xf2, 0x0,
    0x2f, 0xa0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0,
    0xfb, 0x0, 0x0, 0xbe, 0x0, 0x0, 0xcf, 0x20,
    0x0, 0x0, 0xf, 0x90, 0x0, 0x9, 0xf0, 0x0,
    0x7f, 0x70, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0xaf, 0x0, 0x2f, 0xc0, 0x0, 0x0, 0x0, 0xc,
    0xe0, 0x0, 0xe, 0xc0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xb3, 0x3b, 0xf5, 0x6, 0xf7,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf7,
    0x2, 0xfc, 0x2, 0xcf, 0xfd, 0x40, 0x0, 0x0,
    0x14, 0x41, 0x0, 0xcf, 0x31, 0xee, 0x76, 0xdf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x8f,
    0x30, 0x1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xd0, 0xd, 0xd0, 0x0, 0xa, 0xf0, 0x0, 0x0,
    0x0, 0xb, 0xf3, 0x0, 0xfa, 0x0, 0x0, 0x8f,
    0x20, 0x0, 0x0, 0x6, 0xf8, 0x0, 0xe, 0xa0,
    0x0, 0x8, 0xf1, 0x0, 0x0, 0x2, 0xfd, 0x0,
    0x0, 0xcd, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0,
    0xbf, 0x30, 0x0, 0x7, 0xf3, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0x6f, 0x80, 0x0, 0x0, 0xd, 0xe7,
    0x6d, 0xe2, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xb2, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x7d, 0xfe, 0xc5, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xee, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x6f, 0xf3, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x6f, 0xf1, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0x4c, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xf8, 0xef, 0xc0, 0x0, 0x6, 0x10,
    0xc, 0xfc, 0x10, 0x2e, 0xfc, 0x0, 0x4f, 0xd0,
    0x6f, 0xe1, 0x0, 0x2, 0xef, 0xc0, 0x9f, 0x80,
    0xbf, 0x90, 0x0, 0x0, 0x2e, 0xfc, 0xff, 0x30,
    0xdf, 0x80, 0x0, 0x0, 0x2, 0xef, 0xfb, 0x0,
    0xbf, 0xe1, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x10,
    0x3f, 0xfe, 0x63, 0x13, 0x6d, 0xff, 0xef, 0xc1,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x2e, 0xf8,
    0x0, 0x18, 0xdf, 0xfe, 0xb5, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x6f, 0xa6, 0xfa, 0x5f, 0x95, 0xf9, 0x5f, 0x85,
    0xf8, 0x4f, 0x80, 0x10,

    /* U+0028 "(" */
    0x0, 0xe, 0xf6, 0x0, 0x6f, 0xe0, 0x0, 0xef,
    0x70, 0x3, 0xff, 0x10, 0x9, 0xfc, 0x0, 0xd,
    0xf7, 0x0, 0xf, 0xf4, 0x0, 0x3f, 0xf1, 0x0,
    0x6f, 0xe0, 0x0, 0x7f, 0xd0, 0x0, 0x8f, 0xc0,
    0x0, 0x9f, 0xb0, 0x0, 0x9f, 0xb0, 0x0, 0x8f,
    0xc0, 0x0, 0x7f, 0xd0, 0x0, 0x6f, 0xe0, 0x0,
    0x3f, 0xf1, 0x0, 0xf, 0xf4, 0x0, 0xd, 0xf7,
    0x0, 0x8, 0xfc, 0x0, 0x3, 0xff, 0x10, 0x0,
    0xdf, 0x70, 0x0, 0x6f, 0xe0, 0x0, 0xe, 0xf6,

    /* U+0029 ")" */
    0xe, 0xf5, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x1,
    0xff, 0x50, 0x0, 0xa, 0xfa, 0x0, 0x0, 0x5f,
    0xf0, 0x0, 0x1, 0xff, 0x40, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0xaf, 0xa0, 0x0, 0x8, 0xfd, 0x0,
    0x0, 0x6f, 0xe0, 0x0, 0x5, 0xff, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x5f,
    0xf0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x8f, 0xd0,
    0x0, 0xa, 0xfa, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x1f, 0xf4, 0x0, 0x5, 0xff, 0x0, 0x0, 0xaf,
    0xa0, 0x0, 0x1f, 0xf5, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0xef, 0x50, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xbb, 0x0, 0x0, 0x1, 0x0, 0xbb,
    0x0, 0x10, 0x3f, 0x80, 0xbb, 0x8, 0xf3, 0x2b,
    0xfe, 0xee, 0xef, 0xb2, 0x0, 0x5e, 0xff, 0xe5,
    0x0, 0x0, 0x8f, 0xff, 0xf9, 0x10, 0x3e, 0xfa,
    0xcc, 0xaf, 0xe3, 0x1d, 0x40, 0xbb, 0x4, 0xd1,
    0x0, 0x0, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x99,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x5, 0x72, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x1, 0x11, 0x1c, 0xf5, 0x11, 0x10,
    0x0, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x0, 0x0,

    /* U+002D "-" */
    0x12, 0x22, 0x22, 0x20, 0x9f, 0xff, 0xff, 0xf2,
    0x9f, 0xff, 0xff, 0xf2,

    /* U+002E "." */
    0x17, 0x60, 0xaf, 0xf5, 0xcf, 0xf7, 0x4e, 0xc1,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x48, 0x50, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x0, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x5b, 0xef, 0xea, 0x30, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xcf, 0xfc, 0x65, 0x8e, 0xff, 0x70, 0x0, 0x7f,
    0xf7, 0x0, 0x0, 0xc, 0xff, 0x20, 0xf, 0xfb,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x4, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x9f, 0xe0, 0x8f, 0xf0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x3a, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0xcf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x6c, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf6, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x58, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf3, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0xff, 0xa0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x7, 0xff, 0x70, 0x0, 0x0, 0xcf, 0xf2,
    0x0, 0xc, 0xff, 0xc6, 0x58, 0xef, 0xf8, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x6, 0xbe, 0xfe, 0xa3, 0x0, 0x0,

    /* U+0031 "1" */
    0xcf, 0xff, 0xff, 0xac, 0xff, 0xff, 0xfa, 0x34,
    0x44, 0xef, 0xa0, 0x0, 0xd, 0xfa, 0x0, 0x0,
    0xdf, 0xa0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0xdf,
    0xa0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0xdf, 0xa0,
    0x0, 0xd, 0xfa, 0x0, 0x0, 0xdf, 0xa0, 0x0,
    0xd, 0xfa, 0x0, 0x0, 0xdf, 0xa0, 0x0, 0xd,
    0xfa, 0x0, 0x0, 0xdf, 0xa0, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0xdf, 0xa0, 0x0, 0xd, 0xfa,

    /* U+0032 "2" */
    0x0, 0x6, 0xbe, 0xff, 0xd8, 0x10, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x5f, 0xff,
    0xa6, 0x57, 0xbf, 0xff, 0x20, 0xb, 0xc1, 0x0,
    0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xe5, 0x44, 0x44, 0x44, 0x42,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+0033 "3" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4, 0x44,
    0x44, 0x44, 0x4e, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x72, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x7, 0x78, 0xcf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x2e, 0x50, 0x0, 0x0, 0x2, 0xef,
    0xc0, 0xaf, 0xfe, 0x96, 0x57, 0xaf, 0xff, 0x40,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x38, 0xce, 0xfe, 0xc8, 0x10, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfb, 0x0,
    0x3, 0xcc, 0x10, 0x0, 0x0, 0xb, 0xfe, 0x10,
    0x0, 0x4f, 0xf1, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x0, 0x4, 0xff, 0x10, 0x0, 0x5, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x44,
    0x44, 0x44, 0x44, 0x47, 0xff, 0x54, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10,
    0x0,

    /* U+0035 "5" */
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xaf,
    0xc4, 0x44, 0x44, 0x44, 0x20, 0x0, 0xbf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x74, 0x43, 0x10, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xfd, 0x80, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x1, 0x5b, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf6, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf5, 0xd, 0x91, 0x0, 0x0, 0x1, 0xcf,
    0xf0, 0x6f, 0xff, 0xa7, 0x56, 0x9f, 0xff, 0x70,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x17, 0xbe, 0xff, 0xd9, 0x30, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x18, 0xce, 0xfe, 0xc8, 0x10, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x9f,
    0xfe, 0x85, 0x45, 0x8d, 0x20, 0x5, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x4b, 0xff, 0xfe, 0x81, 0x0,
    0xcf, 0xb9, 0xff, 0xff, 0xff, 0xfe, 0x30, 0xcf,
    0xff, 0xe5, 0x10, 0x28, 0xff, 0xe0, 0xbf, 0xfe,
    0x10, 0x0, 0x0, 0x4f, 0xf6, 0x9f, 0xf7, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0x6f, 0xf5, 0x0, 0x0,
    0x0, 0xb, 0xfb, 0x1f, 0xf7, 0x0, 0x0, 0x0,
    0xd, 0xf9, 0xa, 0xfe, 0x20, 0x0, 0x0, 0x5f,
    0xf4, 0x1, 0xef, 0xf7, 0x32, 0x49, 0xff, 0xc0,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x6b, 0xef, 0xeb, 0x50, 0x0,

    /* U+0037 "7" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f, 0xf6,
    0x44, 0x44, 0x44, 0x4f, 0xfa, 0x4f, 0xf2, 0x0,
    0x0, 0x0, 0x6f, 0xf3, 0x4f, 0xf2, 0x0, 0x0,
    0x0, 0xdf, 0xc0, 0x17, 0x71, 0x0, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x5, 0xae, 0xff, 0xeb, 0x60, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xb, 0xff,
    0xb5, 0x22, 0x4a, 0xff, 0xc0, 0x2f, 0xf8, 0x0,
    0x0, 0x0, 0x7f, 0xf3, 0x3f, 0xf3, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0x1f, 0xf7, 0x0, 0x0, 0x0,
    0x5f, 0xf2, 0x9, 0xff, 0x82, 0x0, 0x27, 0xff,
    0xa0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x1d,
    0xff, 0x83, 0x10, 0x28, 0xff, 0xe1, 0x7f, 0xf3,
    0x0, 0x0, 0x0, 0x2f, 0xf9, 0xcf, 0xb0, 0x0,
    0x0, 0x0, 0xa, 0xfd, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xcf, 0xc0, 0x0, 0x0, 0x0,
    0xb, 0xfd, 0x7f, 0xf5, 0x0, 0x0, 0x0, 0x4f,
    0xf9, 0x1e, 0xff, 0xa4, 0x22, 0x4a, 0xff, 0xe1,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x5, 0xad, 0xff, 0xeb, 0x60, 0x0,

    /* U+0039 "9" */
    0x0, 0x2, 0x9d, 0xff, 0xd9, 0x20, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x5,
    0xff, 0xd6, 0x32, 0x5c, 0xff, 0x60, 0x0, 0xdf,
    0xc0, 0x0, 0x0, 0x9, 0xff, 0x10, 0x2f, 0xf4,
    0x0, 0x0, 0x0, 0x1f, 0xf8, 0x4, 0xff, 0x20,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x3f, 0xf4, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0xef, 0xc0, 0x0,
    0x0, 0x9, 0xff, 0xf2, 0x7, 0xff, 0xd5, 0x32,
    0x5c, 0xfe, 0xff, 0x30, 0x9, 0xff, 0xff, 0xff,
    0xfd, 0x6f, 0xf2, 0x0, 0x4, 0xae, 0xfe, 0xc6,
    0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0x0, 0xb, 0xa6, 0x44, 0x6c, 0xff, 0xe1, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x5, 0xbe, 0xff, 0xda, 0x40, 0x0, 0x0,

    /* U+003A ":" */
    0x4e, 0xc1, 0xcf, 0xf7, 0xaf, 0xf5, 0x17, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0x60, 0xaf, 0xf5,
    0xcf, 0xf7, 0x4e, 0xc1,

    /* U+003B ";" */
    0x4e, 0xc1, 0xcf, 0xf7, 0xaf, 0xf5, 0x17, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x20, 0x7f, 0xf3,
    0xcf, 0xf7, 0x7f, 0xf5, 0xd, 0xf0, 0x1f, 0xb0,
    0x5f, 0x60, 0x8f, 0x10,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x0, 0x0,
    0x0, 0x1, 0x8e, 0xfe, 0x0, 0x0, 0x4, 0xbf,
    0xff, 0xc5, 0x0, 0x17, 0xdf, 0xff, 0x93, 0x0,
    0x19, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x5f, 0xfa,
    0x30, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x82, 0x0,
    0x0, 0x0, 0x5, 0xbf, 0xff, 0xb5, 0x0, 0x0,
    0x0, 0x2, 0x8e, 0xff, 0xe8, 0x20, 0x0, 0x0,
    0x0, 0x6c, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,

    /* U+003D "=" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11,
    0x11, 0x10, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+003E ">" */
    0x49, 0x20, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfb,
    0x50, 0x0, 0x0, 0x0, 0x19, 0xff, 0xfe, 0x81,
    0x0, 0x0, 0x0, 0x6, 0xcf, 0xff, 0xa4, 0x0,
    0x0, 0x0, 0x3, 0x9f, 0xff, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xfe, 0x0, 0x0, 0x0, 0x5,
    0xbf, 0xfd, 0x0, 0x0, 0x18, 0xef, 0xfe, 0x82,
    0x0, 0x4b, 0xff, 0xfc, 0x50, 0x0, 0x3e, 0xff,
    0xf9, 0x30, 0x0, 0x0, 0x5f, 0xd6, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x17, 0xbe, 0xff, 0xd9, 0x20, 0x0, 0x5e,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x6f, 0xfe, 0x85,
    0x45, 0xaf, 0xff, 0x22, 0xcb, 0x10, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x80, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x5, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xd2, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x3, 0x8c, 0xef, 0xfe, 0xc8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf,
    0xff, 0xdb, 0xbd, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfa, 0x40, 0x0, 0x0, 0x3,
    0x9f, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xb0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x3a, 0xdf, 0xe9, 0x20,
    0xff, 0x3a, 0xf9, 0x0, 0x4, 0xfe, 0x10, 0x6,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x30, 0xcf, 0x30,
    0xc, 0xf4, 0x0, 0x4f, 0xfb, 0x30, 0x16, 0xef,
    0xff, 0x30, 0x2f, 0xb0, 0x2f, 0xc0, 0x0, 0xef,
    0xa0, 0x0, 0x0, 0x2f, 0xff, 0x30, 0xb, 0xf1,
    0x7f, 0x60, 0x4, 0xff, 0x10, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x5, 0xf5, 0xaf, 0x30, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x30, 0x2, 0xf8,
    0xbf, 0x10, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x1, 0xf9, 0xcf, 0x0, 0x9, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x1, 0xfa,
    0xbf, 0x10, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x30, 0x2, 0xf9, 0xaf, 0x30, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x7, 0xff, 0x30, 0x4, 0xf6,
    0x7f, 0x70, 0x0, 0xef, 0xa0, 0x0, 0x0, 0x2e,
    0xff, 0x40, 0x9, 0xf3, 0x2f, 0xc0, 0x0, 0x4f,
    0xfb, 0x30, 0x16, 0xef, 0xef, 0xa0, 0x4f, 0xc0,
    0xc, 0xf4, 0x0, 0x6, 0xff, 0xff, 0xff, 0xf5,
    0x6f, 0xff, 0xff, 0x30, 0x4, 0xfe, 0x10, 0x0,
    0x3a, 0xef, 0xea, 0x20, 0x8, 0xef, 0xc3, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfa, 0x40, 0x0, 0x0, 0x15,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf,
    0xff, 0xdc, 0xcd, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe, 0xb7,
    0x10, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xef, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfa, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0xf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0, 0x8,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x50, 0x1, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0x0, 0xaf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x90,
    0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x1f, 0xf5, 0x11, 0x11, 0x11,
    0x11, 0x2f, 0xf5, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x5, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xa0, 0xc, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf1,

    /* U+0042 "B" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x20, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0xf3, 0x11, 0x11, 0x23, 0x8f, 0xff, 0x30,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x3, 0xff, 0x90,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xb0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xa0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60,
    0x6f, 0xf3, 0x22, 0x22, 0x24, 0x8f, 0xfc, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x27, 0xff, 0xd0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf9,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,
    0x6f, 0xf3, 0x22, 0x22, 0x22, 0x48, 0xff, 0xf1,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x71, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x3, 0x8c, 0xff, 0xec, 0x72, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x2d, 0xff, 0xfa, 0x75, 0x69, 0xef,
    0xfb, 0x0, 0xd, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x9f, 0x60, 0xa, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x2, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x1,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x9, 0xf7, 0x0,
    0x2, 0xef, 0xff, 0xa7, 0x56, 0x9e, 0xff, 0xb0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x39, 0xdf, 0xfe, 0xc7, 0x20,
    0x0,

    /* U+0044 "D" */
    0x6f, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x10, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6f, 0xf5, 0x44, 0x44, 0x57, 0xbf,
    0xff, 0xb0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0xfa, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x50, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x6f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf5,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf6, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf6, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf5, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf2, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xd0, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xfa, 0x0, 0x6f,
    0xf5, 0x44, 0x44, 0x57, 0xbf, 0xff, 0xb0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x10,
    0x0, 0x0,

    /* U+0045 "E" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x6f, 0xf5,
    0x44, 0x44, 0x44, 0x44, 0x40, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x33, 0x33, 0x33, 0x33, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf5, 0x44, 0x44, 0x44, 0x44, 0x41,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+0046 "F" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xf5, 0x44,
    0x44, 0x44, 0x44, 0x46, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x6f, 0xf5, 0x44, 0x44, 0x44, 0x43, 0x6,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xec, 0x83, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x2d, 0xff, 0xfa, 0x75, 0x69, 0xdf,
    0xfd, 0x10, 0xd, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x6f, 0x90, 0xa, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x2, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0xc, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf2, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x27, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x2f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0xaf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0xdf, 0xf9, 0x10, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x2, 0xdf, 0xff, 0xa7, 0x56, 0x8d, 0xff, 0xf1,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x38, 0xcf, 0xfe, 0xc9, 0x30,
    0x0,

    /* U+0048 "H" */
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf5, 0x44, 0x44, 0x44, 0x44, 0x4d, 0xfa,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,

    /* U+0049 "I" */
    0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2,
    0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2,
    0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2,
    0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2, 0x6f, 0xf2,
    0x6f, 0xf2, 0x6f, 0xf2,

    /* U+004A "J" */
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x14, 0x44, 0x44,
    0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x10,
    0x0, 0x0, 0x4f, 0xf3, 0x7, 0xd1, 0x0, 0x0,
    0xbf, 0xf0, 0xf, 0xfe, 0x84, 0x5b, 0xff, 0x90,
    0x4, 0xef, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x18,
    0xdf, 0xfd, 0x70, 0x0,

    /* U+004B "K" */
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xe1,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x20,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0xa, 0xff, 0x40, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0xaf, 0xf5, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x9, 0xff, 0x60, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x7, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x6f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfc, 0x4f, 0xfd, 0x10, 0x0, 0x0,
    0x6f, 0xff, 0xc0, 0x6, 0xff, 0xb0, 0x0, 0x0,
    0x6f, 0xfd, 0x10, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x2e, 0xfd, 0x10,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,

    /* U+004C "L" */
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x54, 0x44, 0x44, 0x44, 0x42,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x96, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+004D "M" */
    0x6f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf3, 0x6f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf3, 0x6f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf3,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf3, 0x6f, 0xfb, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xaf, 0xf3, 0x6f, 0xf2, 0xff, 0x60,
    0x0, 0x0, 0x8, 0xfd, 0x3f, 0xf3, 0x6f, 0xf0,
    0x7f, 0xe1, 0x0, 0x0, 0x2f, 0xf5, 0x2f, 0xf3,
    0x6f, 0xf0, 0xe, 0xf9, 0x0, 0x0, 0xaf, 0xb0,
    0x2f, 0xf3, 0x6f, 0xf0, 0x5, 0xff, 0x20, 0x3,
    0xff, 0x20, 0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0xcf,
    0xb0, 0xc, 0xf9, 0x0, 0x2f, 0xf3, 0x6f, 0xf0,
    0x0, 0x2f, 0xf4, 0x6f, 0xe1, 0x0, 0x2f, 0xf3,
    0x6f, 0xf0, 0x0, 0x9, 0xfd, 0xef, 0x60, 0x0,
    0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0x1, 0xef, 0xfd,
    0x0, 0x0, 0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0x0,
    0x6f, 0xf4, 0x0, 0x0, 0x2f, 0xf3, 0x6f, 0xf0,
    0x0, 0x0, 0xb, 0x90, 0x0, 0x0, 0x2f, 0xf3,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf3,

    /* U+004E "N" */
    0x6f, 0xe2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xff, 0x90, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xfe, 0xff, 0x30, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf5, 0xff, 0xe1, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x5f, 0xfb, 0x0, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x9, 0xff, 0x80, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0xcf, 0xf5, 0x0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x1e, 0xff, 0x20, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x4, 0xff, 0xd0, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x7f, 0xfa, 0xd, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0xa, 0xff, 0x7d, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa,

    /* U+004F "O" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfc, 0x83, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xfa, 0x75,
    0x79, 0xff, 0xfd, 0x20, 0x0, 0xd, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x9f, 0xfd, 0x0, 0x9, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa, 0x2,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf2, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x7a, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfa, 0xcf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xcc, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfc, 0xaf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xa7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf7, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x20, 0xaf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xa0, 0x0, 0xdf, 0xf9,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0x2,
    0xdf, 0xff, 0xa7, 0x57, 0x9f, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xcf, 0xff, 0xc9,
    0x30, 0x0, 0x0,

    /* U+0050 "P" */
    0x6f, 0xff, 0xff, 0xff, 0xec, 0x82, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x6f,
    0xf5, 0x44, 0x44, 0x58, 0xef, 0xf9, 0x6, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x86, 0xff, 0x20,
    0x0, 0x0, 0x0, 0xd, 0xfb, 0x6f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xb6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xf, 0xf9, 0x6f, 0xf2, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x46, 0xff, 0x20, 0x0, 0x1,
    0x4a, 0xff, 0xc0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x70, 0x0, 0x6f, 0xf5, 0x44, 0x44, 0x31, 0x0,
    0x0, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfc, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xfa,
    0x75, 0x7a, 0xff, 0xfd, 0x20, 0x0, 0x0, 0xdf,
    0xf9, 0x10, 0x0, 0x0, 0x19, 0xff, 0xd0, 0x0,
    0x9, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf9, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x10, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x70, 0xaf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xa0,
    0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xc0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xb0, 0xaf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xa0, 0x7f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x10, 0xb, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfa, 0x0, 0x1, 0xef, 0xf8, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe1, 0x0, 0x0, 0x3e,
    0xff, 0xe8, 0x54, 0x58, 0xef, 0xfe, 0x20, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xae, 0xff, 0xff,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x80, 0x0, 0x1, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x31, 0x5d, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3a, 0xef, 0xd7, 0x0,

    /* U+0052 "R" */
    0x6f, 0xff, 0xff, 0xff, 0xec, 0x92, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x6f, 0xf5, 0x44, 0x44, 0x58, 0xef, 0xf9, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xff, 0x30,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x1, 0xff, 0x80,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xb0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xb0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xff, 0x90,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x8, 0xff, 0x40,
    0x6f, 0xf2, 0x0, 0x0, 0x14, 0xbf, 0xfb, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x6f, 0xf5, 0x33, 0x33, 0x5f, 0xf7, 0x0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x9, 0xff, 0x20, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xef, 0xc0, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x1, 0xef, 0xb0,

    /* U+0053 "S" */
    0x0, 0x4, 0xad, 0xff, 0xeb, 0x71, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xb6, 0x44, 0x59, 0xef, 0x60, 0x4f, 0xf7, 0x0,
    0x0, 0x0, 0x6, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xd7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xb6, 0x10, 0x0, 0x0,
    0x2, 0x8c, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x15, 0x9e, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x6c, 0x30, 0x0, 0x0, 0x0, 0x7f,
    0xf3, 0xcf, 0xfc, 0x85, 0x34, 0x6b, 0xff, 0xb0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x28, 0xce, 0xff, 0xda, 0x40, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x34,
    0x44, 0x44, 0xef, 0xb4, 0x44, 0x44, 0x20, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4,
    0x7f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0xf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xef, 0xc0,
    0x9, 0xff, 0x70, 0x0, 0x0, 0xa, 0xff, 0x50,
    0x1, 0xdf, 0xfd, 0x75, 0x58, 0xef, 0xfb, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xfd, 0x93, 0x0, 0x0,

    /* U+0056 "V" */
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x90, 0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x0, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfc, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x1f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0, 0x0,
    0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x0, 0x4, 0xff, 0x70, 0x0, 0x0, 0x7,
    0xff, 0x10, 0x0, 0x0, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x6f, 0xf4,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x20, 0x3, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x9f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0, 0x1f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x67, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x50,
    0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0xb, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xd0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xdf, 0x80, 0x1, 0xff, 0x80, 0x0, 0x0, 0x2,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x7, 0xfc, 0x9f,
    0xc0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x6f,
    0xf2, 0x0, 0x0, 0xd, 0xf7, 0x4f, 0xf2, 0x0,
    0x0, 0xd, 0xf8, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x0, 0x2f, 0xf2, 0xe, 0xf7, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0, 0x8f,
    0xc0, 0x9, 0xfc, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x7, 0xff, 0x10, 0x0, 0xdf, 0x70, 0x4,
    0xff, 0x10, 0x0, 0xdf, 0x90, 0x0, 0x0, 0x1,
    0xff, 0x70, 0x2, 0xff, 0x20, 0x0, 0xef, 0x70,
    0x2, 0xff, 0x40, 0x0, 0x0, 0x0, 0xcf, 0xc0,
    0x8, 0xfd, 0x0, 0x0, 0x9f, 0xc0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0xd, 0xf7,
    0x0, 0x0, 0x4f, 0xf1, 0xc, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf6, 0x3f, 0xf2, 0x0, 0x0,
    0xe, 0xf6, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfb, 0x8f, 0xd0, 0x0, 0x0, 0x9, 0xfc,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xef, 0x70, 0x0, 0x0, 0x4, 0xff, 0xdf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfb,
    0x0, 0x3f, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0x10, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x6, 0xff,
    0x40, 0x0, 0x0, 0xcf, 0xe1, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x2, 0xff, 0xb0, 0x0, 0xcf,
    0xd0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x8f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x6f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xdf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xb1, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xe1, 0x4, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x7f, 0xf5, 0x0, 0x8, 0xff, 0x40, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0xd, 0xfe, 0x10, 0x0,
    0xd, 0xfe, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x0,
    0x9, 0xff, 0x40, 0x0, 0x0, 0x0, 0x6f, 0xf6,
    0x5, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf2,

    /* U+0059 "Y" */
    0xc, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xe1, 0x3, 0xff, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x60, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0,
    0xb, 0xfc, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0x6, 0xff, 0x30,
    0x0, 0x0, 0xdf, 0x90, 0x0, 0x0, 0x0, 0xdf,
    0xc0, 0x0, 0x7, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4f, 0xf6, 0x0, 0x1f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfe, 0x10, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x93, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x34,
    0x44, 0x44, 0x44, 0x44, 0x6f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xc4, 0x44, 0x44, 0x44, 0x44, 0x43,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+005B "[" */
    0x6f, 0xff, 0xfd, 0x6f, 0xff, 0xfd, 0x6f, 0xf1,
    0x11, 0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f,
    0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0,
    0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0,
    0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f,
    0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0,
    0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0,
    0x0, 0x6f, 0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x6f,
    0xf1, 0x10, 0x6f, 0xff, 0xfd, 0x6f, 0xff, 0xfd,

    /* U+005C "\\" */
    0x68, 0x20, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x9, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x30, 0x0, 0x0, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40,

    /* U+005D "]" */
    0x8f, 0xff, 0xfb, 0x8f, 0xff, 0xfb, 0x1, 0x1b,
    0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0,
    0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb,
    0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb,
    0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0,
    0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb,
    0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb,
    0xfb, 0x0, 0xb, 0xfb, 0x0, 0xb, 0xfb, 0x1,
    0x1b, 0xfb, 0x8f, 0xff, 0xfb, 0x8f, 0xff, 0xfb,

    /* U+005E "^" */
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xaf,
    0x40, 0x0, 0x0, 0x1, 0xfb, 0x2f, 0xb0, 0x0,
    0x0, 0x8, 0xf5, 0xc, 0xf1, 0x0, 0x0, 0xe,
    0xe0, 0x5, 0xf7, 0x0, 0x0, 0x5f, 0x90, 0x0,
    0xfe, 0x0, 0x0, 0xbf, 0x20, 0x0, 0x9f, 0x40,
    0x2, 0xfc, 0x0, 0x0, 0x3f, 0xb0, 0x8, 0xf6,
    0x0, 0x0, 0xc, 0xf1, 0xe, 0xf0, 0x0, 0x0,
    0x6, 0xf8,

    /* U+005F "_" */
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+0060 "`" */
    0x6, 0x87, 0x10, 0x0, 0x1, 0xcf, 0xd1, 0x0,
    0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x7f, 0xd1,

    /* U+0061 "a" */
    0x0, 0x4a, 0xdf, 0xfd, 0x92, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1e, 0xf9, 0x53, 0x48,
    0xff, 0xf1, 0x4, 0x10, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfc, 0x0, 0x7c, 0xef, 0xff,
    0xff, 0xfc, 0xc, 0xff, 0xfd, 0xdd, 0xde, 0xfc,
    0x7f, 0xf5, 0x0, 0x0, 0xa, 0xfc, 0xbf, 0xb0,
    0x0, 0x0, 0xa, 0xfc, 0xbf, 0xb0, 0x0, 0x0,
    0x1f, 0xfc, 0x6f, 0xf6, 0x0, 0x3, 0xdf, 0xfc,
    0xc, 0xff, 0xec, 0xef, 0xfb, 0xfc, 0x0, 0x7c,
    0xff, 0xd9, 0x28, 0xfc,

    /* U+0062 "b" */
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xa0, 0x4b, 0xef, 0xd9,
    0x30, 0x0, 0xbf, 0xa9, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xbf, 0xff, 0xe8, 0x44, 0x7d, 0xff, 0x80,
    0xbf, 0xfd, 0x10, 0x0, 0x0, 0xbf, 0xf3, 0xbf,
    0xf4, 0x0, 0x0, 0x0, 0x1f, 0xf9, 0xbf, 0xd0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xbf, 0xb0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xbf, 0xd0, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0xbf, 0xf4, 0x0, 0x0, 0x0, 0x1f,
    0xf9, 0xbf, 0xfd, 0x20, 0x0, 0x0, 0xbf, 0xf2,
    0xbf, 0xff, 0xe8, 0x44, 0x7d, 0xff, 0x80, 0xbf,
    0x99, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xbf, 0x90,
    0x5b, 0xef, 0xda, 0x30, 0x0,

    /* U+0063 "c" */
    0x0, 0x1, 0x8d, 0xef, 0xd8, 0x10, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xfe, 0x40, 0x6, 0xff, 0xe7,
    0x44, 0x7e, 0xff, 0x21, 0xff, 0xc1, 0x0, 0x0,
    0x1d, 0x90, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfc, 0x10, 0x0, 0x1, 0xd9, 0x0,
    0x6f, 0xfe, 0x74, 0x47, 0xef, 0xf2, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x18, 0xdf,
    0xfd, 0x81, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfc, 0x0, 0x2, 0x9d, 0xfe, 0xb5,
    0xa, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xaa,
    0xfc, 0x7, 0xff, 0xe7, 0x44, 0x8e, 0xff, 0xfc,
    0x2f, 0xfc, 0x10, 0x0, 0x1, 0xdf, 0xfc, 0x8f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0xcf, 0xb0,
    0x0, 0x0, 0x0, 0xd, 0xfc, 0xef, 0x80, 0x0,
    0x0, 0x0, 0xa, 0xfc, 0xef, 0x80, 0x0, 0x0,
    0x0, 0xa, 0xfc, 0xcf, 0xa0, 0x0, 0x0, 0x0,
    0xc, 0xfc, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x2f,
    0xfc, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0xcf, 0xfc,
    0x7, 0xff, 0xc4, 0x11, 0x5d, 0xff, 0xfc, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xb9, 0xfc, 0x0, 0x3,
    0x9d, 0xfe, 0xb5, 0x8, 0xfc,

    /* U+0065 "e" */
    0x0, 0x2, 0x9d, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x7, 0xff,
    0xb4, 0x23, 0x8f, 0xfc, 0x0, 0x2f, 0xf9, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x8f, 0xd0, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x3f, 0xf1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xef, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xd2,
    0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x55, 0x0, 0x6, 0xff, 0xe7,
    0x43, 0x5b, 0xff, 0x20, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x2, 0x8c, 0xef, 0xda,
    0x30, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x6c, 0xfe, 0xb3, 0x0, 0x9, 0xff,
    0xff, 0xf6, 0x0, 0x2f, 0xfa, 0x22, 0x60, 0x0,
    0x5f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xd0, 0xaf, 0xff,
    0xff, 0xff, 0xd0, 0x1, 0x6f, 0xf1, 0x11, 0x10,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x3, 0x9d, 0xfe, 0xc6, 0x5, 0xff, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xc6, 0xff, 0x9, 0xff,
    0xd7, 0x33, 0x6d, 0xff, 0xff, 0x3f, 0xfa, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x9f, 0xe0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xdf, 0x90, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xef, 0x70, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xdf, 0x90, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x4f,
    0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xb, 0xff,
    0xb4, 0x0, 0x3a, 0xff, 0xff, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xea, 0xff, 0x0, 0x6, 0xdf, 0xff,
    0xf9, 0x17, 0xfe, 0x0, 0x0, 0x0, 0x22, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfa, 0x6, 0x90, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0x1e, 0xff, 0x95, 0x33, 0x5b, 0xff, 0xc0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x16,
    0xbe, 0xff, 0xeb, 0x50, 0x0,

    /* U+0068 "h" */
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x5, 0xbe, 0xfe, 0xa2, 0x0, 0xbf,
    0xbb, 0xff, 0xff, 0xff, 0xf4, 0xb, 0xff, 0xfd,
    0x74, 0x49, 0xff, 0xf1, 0xbf, 0xfb, 0x0, 0x0,
    0x6, 0xff, 0x7b, 0xff, 0x20, 0x0, 0x0, 0xd,
    0xfb, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0xaf, 0xcb,
    0xfb, 0x0, 0x0, 0x0, 0x8, 0xfd, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x8f, 0xdb, 0xfa, 0x0, 0x0,
    0x0, 0x8, 0xfd, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x8f, 0xdb, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xfd,
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x8f, 0xdb, 0xfa,
    0x0, 0x0, 0x0, 0x8, 0xfd, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x8f, 0xd0,

    /* U+0069 "i" */
    0x9, 0xf8, 0x1, 0xff, 0xf0, 0xd, 0xfc, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0xb,
    0xfa, 0x0, 0xbf, 0xa0, 0xb, 0xfa, 0x0, 0xbf,
    0xa0, 0xb, 0xfa, 0x0, 0xbf, 0xa0, 0xb, 0xfa,
    0x0, 0xbf, 0xa0, 0xb, 0xfa, 0x0, 0xbf, 0xa0,
    0xb, 0xfa, 0x0, 0xbf, 0xa0, 0xb, 0xfa, 0x0,

    /* U+006A "j" */
    0x0, 0x0, 0x7, 0xfa, 0x0, 0x0, 0x0, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xfd, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0, 0x9f,
    0xc0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0xc, 0xfb, 0x0, 0x74,
    0x27, 0xff, 0x60, 0x1f, 0xff, 0xff, 0xd0, 0x1,
    0x9d, 0xfe, 0x91, 0x0,

    /* U+006B "k" */
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x9,
    0xff, 0x50, 0xbf, 0xa0, 0x0, 0x0, 0xaf, 0xf5,
    0x0, 0xbf, 0xa0, 0x0, 0xa, 0xff, 0x60, 0x0,
    0xbf, 0xa0, 0x0, 0xbf, 0xf6, 0x0, 0x0, 0xbf,
    0xa0, 0xb, 0xff, 0x70, 0x0, 0x0, 0xbf, 0xa0,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0xbc, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc, 0xff,
    0x70, 0x0, 0x0, 0xbf, 0xff, 0x60, 0xcf, 0xf4,
    0x0, 0x0, 0xbf, 0xf5, 0x0, 0x1e, 0xfe, 0x10,
    0x0, 0xbf, 0xa0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0xb, 0xff, 0x30, 0xbf, 0xa0,
    0x0, 0x0, 0x1, 0xef, 0xe1,

    /* U+006C "l" */
    0xbf, 0xab, 0xfa, 0xbf, 0xab, 0xfa, 0xbf, 0xab,
    0xfa, 0xbf, 0xab, 0xfa, 0xbf, 0xab, 0xfa, 0xbf,
    0xab, 0xfa, 0xbf, 0xab, 0xfa, 0xbf, 0xab, 0xfa,
    0xbf, 0xab, 0xfa, 0xbf, 0xa0,

    /* U+006D "m" */
    0xbf, 0x90, 0x7c, 0xff, 0xd8, 0x0, 0x2, 0x9d,
    0xff, 0xc6, 0x0, 0xb, 0xfa, 0xcf, 0xff, 0xff,
    0xfe, 0x16, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xbf,
    0xff, 0xb4, 0x23, 0x9f, 0xfd, 0xff, 0x93, 0x24,
    0xbf, 0xf7, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x9f,
    0xff, 0x70, 0x0, 0x0, 0xcf, 0xe0, 0xbf, 0xf1,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x5,
    0xff, 0x2b, 0xfc, 0x0, 0x0, 0x0, 0xf, 0xf9,
    0x0, 0x0, 0x0, 0x2f, 0xf3, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0xef, 0x70, 0x0, 0x0, 0x2, 0xff,
    0x4b, 0xfa, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x0,
    0x0, 0x0, 0x2f, 0xf4, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0xef, 0x70, 0x0, 0x0, 0x2, 0xff, 0x4b,
    0xfa, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0,
    0x0, 0x2f, 0xf4, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0xef, 0x70, 0x0, 0x0, 0x2, 0xff, 0x4b, 0xfa,
    0x0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0,
    0x2f, 0xf4, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0xef,
    0x70, 0x0, 0x0, 0x2, 0xff, 0x4b, 0xfa, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0x0, 0x2f,
    0xf4,

    /* U+006E "n" */
    0xbf, 0x90, 0x6b, 0xef, 0xea, 0x20, 0xb, 0xfa,
    0xcf, 0xff, 0xff, 0xff, 0x40, 0xbf, 0xff, 0xc5,
    0x22, 0x7f, 0xff, 0x1b, 0xff, 0xa0, 0x0, 0x0,
    0x4f, 0xf7, 0xbf, 0xf1, 0x0, 0x0, 0x0, 0xcf,
    0xbb, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xfc, 0xbf,
    0xb0, 0x0, 0x0, 0x0, 0x8f, 0xdb, 0xfa, 0x0,
    0x0, 0x0, 0x8, 0xfd, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x8f, 0xdb, 0xfa, 0x0, 0x0, 0x0, 0x8,
    0xfd, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x8f, 0xdb,
    0xfa, 0x0, 0x0, 0x0, 0x8, 0xfd, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x8f, 0xdb, 0xfa, 0x0, 0x0,
    0x0, 0x8, 0xfd,

    /* U+006F "o" */
    0x0, 0x2, 0x8d, 0xfe, 0xc7, 0x10, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x6, 0xff,
    0xe7, 0x44, 0x7e, 0xff, 0x40, 0x1f, 0xfc, 0x10,
    0x0, 0x1, 0xdf, 0xe0, 0x8f, 0xf1, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0xc, 0xfa, 0xef, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0xef, 0x80, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x8f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x1f, 0xfc,
    0x10, 0x0, 0x1, 0xdf, 0xe0, 0x6, 0xff, 0xe7,
    0x44, 0x7e, 0xff, 0x40, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x2, 0x8d, 0xfe, 0xc7,
    0x10, 0x0,

    /* U+0070 "p" */
    0xbf, 0x90, 0x5b, 0xef, 0xd9, 0x30, 0x0, 0xbf,
    0x9b, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xbf, 0xff,
    0xe6, 0x22, 0x5c, 0xff, 0x80, 0xbf, 0xfd, 0x10,
    0x0, 0x0, 0xaf, 0xf3, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0xf, 0xf9, 0xbf, 0xd0, 0x0, 0x0, 0x0,
    0x9, 0xfd, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xbf, 0xe0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0xbf,
    0xf4, 0x0, 0x0, 0x0, 0x1f, 0xf9, 0xbf, 0xfe,
    0x20, 0x0, 0x0, 0xcf, 0xf2, 0xbf, 0xff, 0xe8,
    0x44, 0x7d, 0xff, 0x80, 0xbf, 0xa9, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xbf, 0xa0, 0x4b, 0xef, 0xda,
    0x30, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x2, 0x9d, 0xfe, 0xb5, 0x8, 0xfc, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xa8, 0xfc, 0x7, 0xff,
    0xe7, 0x44, 0x8e, 0xff, 0xfc, 0x2f, 0xfc, 0x10,
    0x0, 0x1, 0xdf, 0xfc, 0x8f, 0xf1, 0x0, 0x0,
    0x0, 0x3f, 0xfc, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0xd, 0xfc, 0xef, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0xef, 0x80, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x8f,
    0xf2, 0x0, 0x0, 0x0, 0x3f, 0xfc, 0x2f, 0xfc,
    0x10, 0x0, 0x1, 0xdf, 0xfc, 0x7, 0xff, 0xe7,
    0x44, 0x8e, 0xff, 0xfc, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xaa, 0xfc, 0x0, 0x3, 0x9d, 0xfe, 0xb4,
    0xa, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc,

    /* U+0072 "r" */
    0xbf, 0x90, 0x5b, 0xe6, 0xbf, 0x9a, 0xff, 0xf6,
    0xbf, 0xef, 0xf9, 0x72, 0xbf, 0xfd, 0x10, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x6, 0xce, 0xfe, 0xc8, 0x30, 0x1, 0xcf,
    0xff, 0xff, 0xff, 0xf2, 0x9, 0xff, 0x83, 0x23,
    0x6c, 0xb0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x93, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfc,
    0x94, 0x0, 0x0, 0x17, 0xcf, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x25, 0xcf, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfb, 0x4, 0x10, 0x0, 0x0,
    0xb, 0xfb, 0xe, 0xfa, 0x53, 0x34, 0xaf, 0xf6,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x5a,
    0xdf, 0xfe, 0xb5, 0x0,

    /* U+0074 "t" */
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xd0, 0xaf, 0xff, 0xff, 0xff,
    0xd0, 0x1, 0x6f, 0xf1, 0x11, 0x10, 0x0, 0x6f,
    0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x1f,
    0xfc, 0x33, 0x81, 0x0, 0x8, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6d, 0xfe, 0xb3,

    /* U+0075 "u" */
    0xdf, 0x80, 0x0, 0x0, 0x0, 0xcf, 0xad, 0xf8,
    0x0, 0x0, 0x0, 0xc, 0xfa, 0xdf, 0x80, 0x0,
    0x0, 0x0, 0xcf, 0xad, 0xf8, 0x0, 0x0, 0x0,
    0xc, 0xfa, 0xdf, 0x80, 0x0, 0x0, 0x0, 0xcf,
    0xad, 0xf8, 0x0, 0x0, 0x0, 0xc, 0xfa, 0xdf,
    0x80, 0x0, 0x0, 0x0, 0xcf, 0xad, 0xf8, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0xcf, 0xa0, 0x0, 0x0,
    0x0, 0xef, 0xab, 0xfc, 0x0, 0x0, 0x0, 0x2f,
    0xfa, 0x7f, 0xf4, 0x0, 0x0, 0xb, 0xff, 0xa1,
    0xff, 0xe6, 0x22, 0x4c, 0xff, 0xfa, 0x4, 0xff,
    0xff, 0xff, 0xfb, 0xbf, 0xa0, 0x2, 0x9d, 0xfe,
    0xc6, 0xa, 0xfa,

    /* U+0076 "v" */
    0xd, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0,
    0x6f, 0xf1, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x0,
    0xff, 0x70, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x9,
    0xfd, 0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0xcf,
    0xa0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x5, 0xff,
    0x10, 0x1, 0xff, 0x40, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0xd, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xff, 0x44,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xb, 0xfb, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60, 0x0,
    0x0,

    /* U+0077 "w" */
    0xaf, 0x90, 0x0, 0x0, 0x0, 0xef, 0x70, 0x0,
    0x0, 0x0, 0xff, 0x25, 0xfe, 0x0, 0x0, 0x0,
    0x4f, 0xfd, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0xe,
    0xf4, 0x0, 0x0, 0x9, 0xff, 0xf2, 0x0, 0x0,
    0xb, 0xf7, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0xff,
    0xdf, 0x80, 0x0, 0x1, 0xff, 0x10, 0x4, 0xff,
    0x0, 0x0, 0x5f, 0xd5, 0xfd, 0x0, 0x0, 0x6f,
    0xb0, 0x0, 0xe, 0xf5, 0x0, 0xb, 0xf7, 0xf,
    0xf3, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x8f, 0xa0,
    0x1, 0xff, 0x10, 0xaf, 0x90, 0x2, 0xff, 0x0,
    0x0, 0x3, 0xff, 0x0, 0x6f, 0xb0, 0x4, 0xfe,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0xd, 0xf5, 0xc,
    0xf6, 0x0, 0xe, 0xf4, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x8f, 0xb2, 0xff, 0x0, 0x0, 0x8f, 0x92,
    0xfe, 0x0, 0x0, 0x0, 0x2, 0xff, 0x9f, 0xa0,
    0x0, 0x2, 0xff, 0x9f, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf4, 0x0, 0x0, 0xd, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x0, 0x0,
    0x0, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x80, 0x0, 0x0, 0x1, 0xff, 0x80, 0x0,
    0x0,

    /* U+0078 "x" */
    0x1e, 0xfa, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x5,
    0xff, 0x50, 0x0, 0x8, 0xff, 0x20, 0x0, 0x9f,
    0xf2, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0xd, 0xfc,
    0x1, 0xef, 0x90, 0x0, 0x0, 0x2, 0xff, 0x8a,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xef, 0xf6, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x47, 0xff, 0x20, 0x0, 0x0, 0x2f,
    0xf8, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0xcf, 0xc0,
    0x0, 0x1f, 0xf9, 0x0, 0x8, 0xff, 0x20, 0x0,
    0x5, 0xff, 0x50, 0x4f, 0xf6, 0x0, 0x0, 0x0,
    0xaf, 0xf2,

    /* U+0079 "y" */
    0xd, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0,
    0x6f, 0xf1, 0x0, 0x0, 0x0, 0xe, 0xf5, 0x0,
    0xff, 0x70, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x9,
    0xfe, 0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x2f,
    0xf5, 0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0xbf,
    0xc0, 0x0, 0xa, 0xfa, 0x0, 0x0, 0x4, 0xff,
    0x20, 0x1, 0xff, 0x30, 0x0, 0x0, 0xd, 0xf9,
    0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xf0,
    0xd, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff, 0x64,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0,
    0xd, 0x72, 0x4c, 0xfe, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x7,
    0xdf, 0xea, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x11, 0x11, 0x11, 0x18,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xb0, 0x0, 0x0, 0x0,
    0xc, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x2e, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xd1, 0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5f, 0xf7, 0x11, 0x11, 0x11, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1,

    /* U+007B "{" */
    0x0, 0x2, 0xae, 0xf4, 0x0, 0xe, 0xff, 0xf4,
    0x0, 0x6f, 0xf7, 0x10, 0x0, 0x8f, 0xe0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x2, 0xdf, 0xb0, 0x0, 0x9f, 0xfe, 0x30, 0x0,
    0x9f, 0xff, 0x40, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x9f, 0xd0, 0x0,
    0x0, 0x8f, 0xe0, 0x0, 0x0, 0x6f, 0xf7, 0x10,
    0x0, 0xe, 0xff, 0xf4, 0x0, 0x2, 0xae, 0xf4,

    /* U+007C "|" */
    0x6f, 0xd6, 0xfd, 0x6f, 0xd6, 0xfd, 0x6f, 0xd6,
    0xfd, 0x6f, 0xd6, 0xfd, 0x6f, 0xd6, 0xfd, 0x6f,
    0xd6, 0xfd, 0x6f, 0xd6, 0xfd, 0x6f, 0xd6, 0xfd,
    0x6f, 0xd6, 0xfd, 0x6f, 0xd6, 0xfd, 0x6f, 0xd6,
    0xfd, 0x6f, 0xd6, 0xfd,

    /* U+007D "}" */
    0x8f, 0xd9, 0x10, 0x0, 0x8f, 0xff, 0xb0, 0x0,
    0x2, 0xaf, 0xf2, 0x0, 0x0, 0x1f, 0xf5, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xe, 0xfa, 0x10, 0x0, 0x6, 0xff, 0xf5,
    0x0, 0x7, 0xff, 0xf5, 0x0, 0xf, 0xf9, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0x1f, 0xf5, 0x0, 0x2, 0xaf, 0xf2, 0x0,
    0x8f, 0xff, 0xb0, 0x0, 0x8f, 0xd9, 0x10, 0x0,

    /* U+007E "~" */
    0x2, 0xbf, 0xe8, 0x0, 0x0, 0x8f, 0x0, 0xdf,
    0xff, 0xfd, 0x30, 0x1e, 0xd0, 0x4f, 0xa0, 0x1a,
    0xff, 0xde, 0xf6, 0x7, 0xf2, 0x0, 0x4, 0xcf,
    0xd7, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6a, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xcf, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xae,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x94, 0x3f, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xb7, 0x20, 0x0,
    0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xe9, 0x50, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x23, 0x4f, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x30, 0x0, 0x0, 0x1, 0x9f, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0x30,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x1, 0xe, 0xff, 0x30, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xfc, 0x9, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xf4, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x5c,
    0xef, 0xd9, 0x20, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x76, 0x0, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0x40, 0x6, 0x7f, 0xb3, 0x37, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x33,
    0xbf, 0xff, 0xff, 0xff, 0xfb, 0x99, 0x99, 0x99,
    0x99, 0x9a, 0xff, 0xff, 0xff, 0xff, 0xc6, 0x69,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfb,
    0x66, 0xcf, 0xf9, 0x0, 0x3f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x60, 0x9, 0xff, 0x90,
    0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x9f, 0xfd, 0x88, 0xbf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc8, 0x8d, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfa, 0x11, 0x5f, 0xfa,
    0x88, 0x88, 0x88, 0x88, 0x89, 0xff, 0x81, 0x1a,
    0xff, 0x90, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x9f, 0xf9, 0x0, 0x4f,
    0xfe, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xff, 0x60,
    0x9, 0xff, 0xfd, 0xde, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xdd, 0xff, 0xff, 0xcc,
    0xef, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xec, 0xcf, 0xff, 0x90, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x9f, 0xf9,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x60, 0x9, 0xff, 0xb2, 0x26, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x22, 0xbf,
    0xff, 0xff, 0xff, 0xf8, 0x55, 0x55, 0x55, 0x55,
    0x56, 0xff, 0xff, 0xff, 0xff, 0xd7, 0x7a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x77,
    0xdf, 0xa8, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x8, 0xa0,

    /* U+F00B "" */
    0x14, 0x44, 0x44, 0x20, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x1e, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xd1, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x44, 0x44, 0x20,
    0x2, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x1e, 0xff, 0xff, 0xff, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xd1, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xee, 0xee, 0xc1, 0xb, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0x9f, 0xff, 0xff, 0xff,
    0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x25, 0x55, 0x55, 0x30, 0x2,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x6f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xf7, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x7, 0xea, 0x0, 0x0, 0x0, 0x0, 0x8, 0xe9,
    0x0, 0x7f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xa0, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xf2, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x8f, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xfb,
    0x8, 0xff, 0xff, 0xfd, 0x10, 0x0, 0xcf, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x8, 0xff, 0xff, 0xfd,
    0x2c, 0xff, 0xff, 0xfb, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0xcf, 0xff, 0xff, 0xb0, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0xc, 0xff, 0xff, 0xf2, 0xaf,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xd0,
    0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x10, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xeb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0xbf, 0xff,
    0x20, 0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0xb, 0xff, 0xf2, 0x0, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfd, 0x0, 0xbf,
    0xff, 0x20, 0x6f, 0xff, 0xd1, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xd0, 0xb, 0xff, 0xf2, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xef, 0xff, 0xe2, 0x0,
    0xbf, 0xff, 0x20, 0xa, 0xff, 0xff, 0x50, 0x0,
    0x7f, 0xff, 0xf2, 0x0, 0xb, 0xff, 0xf2, 0x0,
    0xa, 0xff, 0xfe, 0x0, 0xe, 0xff, 0xf6, 0x0,
    0x0, 0xbf, 0xff, 0x20, 0x0, 0x1e, 0xff, 0xf5,
    0x3, 0xff, 0xfe, 0x0, 0x0, 0xb, 0xff, 0xf2,
    0x0, 0x0, 0x7f, 0xff, 0xa0, 0x7f, 0xff, 0x80,
    0x0, 0x0, 0xbf, 0xff, 0x20, 0x0, 0x1, 0xff,
    0xfe, 0x9, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf2, 0x0, 0x0, 0xe, 0xff, 0xf0, 0xaf, 0xff,
    0x40, 0x0, 0x0, 0xbf, 0xff, 0x20, 0x0, 0x0,
    0xdf, 0xff, 0x19, 0xff, 0xf4, 0x0, 0x0, 0x8,
    0xff, 0xe1, 0x0, 0x0, 0xe, 0xff, 0xf0, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x5, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xc0,
    0x1f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf7, 0x0, 0xbf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x20, 0x3, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfa, 0x53,
    0x24, 0x8d, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xab, 0xba, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x46, 0x76, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x81, 0x4, 0xcf, 0xff,
    0xff, 0xff, 0xc4, 0x1, 0x96, 0x0, 0x0, 0x3,
    0xff, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xf4, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x7f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x90,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x79, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xf7, 0x0, 0x0, 0x9, 0xd4, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x4, 0xda, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8a, 0xba, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x80, 0x0,
    0xf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb1, 0x1, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xff, 0xd2, 0x1f, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xfb, 0xaf, 0xff, 0xf6, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf8, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf5, 0x4, 0x50, 0x4e,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xe3, 0x7, 0xff, 0x90, 0x2d, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff,
    0xc1, 0xa, 0xff, 0xff, 0xb1, 0x1b, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x4, 0xff, 0xff, 0xa0, 0x1c,
    0xff, 0xff, 0xff, 0xd2, 0x9, 0xff, 0xff, 0x50,
    0x0, 0x7, 0xff, 0xff, 0x70, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x6, 0xff, 0xff, 0x90, 0x9,
    0xff, 0xff, 0x50, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x3, 0xef, 0xff, 0xb0, 0xcf, 0xfe,
    0x30, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x2, 0xdf, 0xfd, 0x1, 0xec, 0x10, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0xbf, 0x20, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x10, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x98, 0x88,
    0xef, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xcc, 0xcc, 0xc6, 0x0, 0x0, 0x4c,
    0xcc, 0xcc, 0xc3, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbb, 0xbb, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x55, 0x55, 0xcf, 0xff, 0xff,
    0xc5, 0x55, 0x52, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x88, 0x88, 0x88,
    0x85, 0x5, 0xff, 0xf5, 0x5, 0x88, 0x88, 0x88,
    0x84, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5, 0xe5,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x3f, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x77, 0xf8, 0x6f, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0xae, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xb2,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x29, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x1,
    0xef, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x20, 0x0, 0xaf, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfc, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf7, 0xd, 0xff, 0xf6, 0x66, 0x66, 0x50,
    0x0, 0x0, 0x0, 0x4, 0x66, 0x66, 0x6e, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x44, 0x44, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x46, 0x76, 0x30,
    0x0, 0x0, 0x4, 0xde, 0xc0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0, 0x6f,
    0xff, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x6, 0xff, 0xf0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x5f, 0xff, 0x0, 0x6, 0xff, 0xff, 0xfa, 0x51,
    0x2, 0x5b, 0xff, 0xff, 0xfb, 0xff, 0xf0, 0x4,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x3, 0xcf,
    0xff, 0xff, 0xff, 0x0, 0xef, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf0,
    0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x4, 0x54,
    0x43, 0xcf, 0xff, 0xff, 0xd, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfd, 0xf, 0xff, 0xff, 0xc5,
    0x56, 0x76, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x70, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xe0, 0xf, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xff,
    0xf5, 0x0, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0x20,
    0x0, 0x27, 0xef, 0xff, 0xf8, 0x0, 0xf, 0xff,
    0x59, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0xf5, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xf,
    0xff, 0x60, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0x81, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x2, 0x69, 0xa9, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf8, 0x0, 0x11, 0x11, 0x6f, 0xff, 0xff,
    0x8b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x14, 0x55, 0x55, 0xaf, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9e, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x80, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x11,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x1f, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0xef, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x1, 0xef, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x9, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xcf,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xbf, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x2f, 0xf9, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x66, 0x0, 0x25, 0x55, 0x55,
    0xbf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xa1, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x70, 0x0, 0x0, 0x20, 0x0, 0xaf, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf8, 0x0,
    0x0, 0x5f, 0xc1, 0x0, 0xcf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x80, 0x0, 0x5,
    0xff, 0xe1, 0x2, 0xff, 0xa0, 0x0, 0x1, 0x11,
    0x16, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x6, 0xff,
    0xc0, 0x7, 0xff, 0x20, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x2, 0x20, 0x5, 0xff, 0x60,
    0x1f, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0xff, 0x50, 0xb, 0xfd, 0x0, 0xaf,
    0xc0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x1e, 0xff, 0x20, 0x4f, 0xf2, 0x7, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1e,
    0xf9, 0x0, 0xff, 0x50, 0x4f, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x9f, 0xb0,
    0xe, 0xf6, 0x3, 0xff, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xc, 0xfa, 0x0, 0xff,
    0x60, 0x4f, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xb, 0xff, 0x40, 0x3f, 0xf3, 0x6,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x2, 0xff, 0x90, 0x9, 0xff, 0x0, 0xaf, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5,
    0x50, 0x3, 0xff, 0x80, 0xe, 0xf8, 0x1, 0x45,
    0x55, 0x5a, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x5, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x80, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0xdf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x0, 0x7f, 0xe3, 0x0, 0x9f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x70, 0x0, 0x0, 0x61, 0x0, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x8, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xb8, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x62, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x60,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc9, 0xcf,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x32, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x2, 0xef, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0x32, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x5, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xef,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xf3, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xef, 0xf3, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xbf, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x7f, 0xfe, 0x11, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0xe, 0xff, 0xc1, 0x4,
    0x7e, 0xff, 0xff, 0xff, 0x20, 0x6, 0xff, 0xfe,
    0x61, 0xc, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xdf, 0xff, 0xe9, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F048 "" */
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xf4,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfa,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xfa, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf9,
    0xac, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xfc, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04C "" */
    0x0, 0x22, 0x22, 0x20, 0x0, 0x0, 0x0, 0x12,
    0x22, 0x21, 0x0, 0x3e, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xe3, 0xdf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xfe, 0xaf,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xf9, 0x8, 0xbb, 0xbb, 0xba, 0x30, 0x0,
    0x3, 0xab, 0xbb, 0xbb, 0x70,

    /* U+F04D "" */
    0x0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0x80,

    /* U+F051 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf8, 0xf, 0xff, 0xc1, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x90, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0xef, 0xf9, 0xf, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0xe, 0xff, 0x90, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xf9, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xe, 0xff, 0x90, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xef, 0xf9, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xe, 0xff, 0x90, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xf9, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x3e, 0xff, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x20, 0xef, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xe, 0xff,
    0x90, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0xef,
    0xf9, 0xf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x90, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xef, 0xf9, 0xf, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x90, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf9, 0x5, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xcc, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xde, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xed, 0x40, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x54, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdd, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0xa, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xfc, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0xbf,
    0xff, 0xfa, 0x88, 0x88, 0x88, 0x71, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x1, 0x22, 0x22, 0x22, 0x7f, 0xff,
    0xf6, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xbb,
    0x70, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x4a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xa2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x69, 0xbd, 0xdc,
    0xa6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xcf, 0xff, 0xff, 0xa8, 0x8a, 0xef, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xef,
    0xd5, 0x0, 0x3f, 0xff, 0xff, 0xa0, 0x0, 0x5,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xf8,
    0x0, 0x9f, 0xff, 0xff, 0x70, 0x1, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xef, 0xff, 0xf3, 0x3,
    0xff, 0xff, 0xff, 0x20, 0xaf, 0xff, 0xff, 0xf2,
    0x3, 0x34, 0xcf, 0xff, 0xff, 0x90, 0xf, 0xff,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0xef, 0xff, 0xff,
    0xf1, 0xbf, 0xff, 0xff, 0xf1, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xfd, 0x2,
    0xff, 0xff, 0xff, 0x40, 0x3f, 0xff, 0xff, 0xff,
    0xf5, 0x3, 0xff, 0xff, 0xff, 0x40, 0x7, 0xff,
    0xff, 0xfa, 0x0, 0x9f, 0xff, 0xff, 0xfb, 0x0,
    0x8f, 0xff, 0xff, 0x90, 0x0, 0x9, 0xff, 0xff,
    0xf3, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x2f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xe2,
    0x0, 0x4, 0x41, 0x0, 0x1d, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xfd, 0x86, 0x68,
    0xcf, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x8c, 0xef, 0xfe, 0xc9, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x2, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x70, 0x0, 0x2,
    0x6a, 0xcd, 0xcb, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa2, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xfe, 0xa8, 0x9c, 0xff, 0xff, 0xf9, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xfb, 0x16, 0xff, 0xa1, 0x0, 0x9f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8, 0x10,
    0x0, 0x2c, 0xff, 0xfd, 0x7f, 0xff, 0xe1, 0x0,
    0xef, 0xff, 0xff, 0x20, 0x0, 0x0, 0x8, 0xfe,
    0x30, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x9, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2, 0xff,
    0xff, 0x60, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x5f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xa0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xf3, 0x4, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0x10, 0x6f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf8, 0x9, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xff,
    0xfa, 0x75, 0x61, 0x0, 0x2, 0xdf, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0xaf, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xad, 0xef, 0xfd, 0xa0, 0x0, 0x0, 0x6f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xde,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfb, 0xaa,
    0xaf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf0, 0x0, 0xc,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf0, 0x0, 0xd, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xf0, 0x0, 0xe, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf1, 0x0, 0xf, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0xf, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xfd, 0xbb,
    0xcf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x54, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x32, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfe, 0x20, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xfe, 0x2f, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcc,
    0xdd, 0xde, 0xff, 0xff, 0x80, 0xc, 0xff, 0xff,
    0xee, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa, 0xff,
    0xf3, 0xb, 0xff, 0xff, 0x90, 0x4f, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xb, 0xf4, 0xb, 0xff, 0xff,
    0x90, 0x3, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x1d, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xb0, 0x10, 0x0, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xb0, 0x3f, 0x60, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0x60,
    0x4f, 0xff, 0xb0, 0x9, 0xbb, 0xbb, 0xff, 0xff,
    0xc0, 0xb, 0xff, 0xff, 0xbc, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0x42, 0x33, 0x33,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x13, 0x36, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x20, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x9c, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0,
    0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0x90, 0x0, 0xc, 0xff, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfa, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xa0, 0xaf, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf6, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf4, 0x9, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F078 "" */
    0x5, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8c, 0x20, 0x5f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xe2, 0xbf, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf7, 0x3f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xd1, 0x3, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xfd, 0x10, 0x0,
    0x3f, 0xff, 0xff, 0x50, 0x0, 0x9, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf5, 0x0,
    0x9f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x59, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x7, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x1, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x50, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x5, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xdf, 0xff, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0xaf, 0xfb, 0x3f, 0xfe, 0x2e, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0xa, 0xa0, 0x3f, 0xfe, 0x2, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x99, 0x3, 0xff, 0xe0, 0x2b, 0x60,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa4, 0xff, 0xe2, 0xef, 0xf4,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xec, 0x20, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0xa, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x9f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x1, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x0, 0x0, 0x9, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0x40, 0x0, 0x0,

    /* U+F07B "" */
    0x8, 0xbc, 0xcc, 0xcc, 0xcc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa9, 0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xaa, 0xaa, 0xff, 0xff, 0xff,
    0xca, 0xaa, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x88, 0x88, 0x88,
    0x60, 0xdf, 0xff, 0xff, 0x60, 0x68, 0x88, 0x88,
    0x84, 0xff, 0xff, 0xff, 0xff, 0x7, 0xef, 0xff,
    0xd2, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x99,
    0x99, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x3f, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x77, 0xf8, 0x6f, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xb7,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x39, 0x40, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xf2, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x1, 0x8e, 0xff, 0xff, 0xfd, 0x10,
    0x2b, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xc9, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xec, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x1, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x47, 0x61, 0x0, 0x1e,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xf6, 0x9, 0xff, 0xfe, 0xff, 0xfe, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xa0, 0xef, 0xf7,
    0x2, 0xff, 0xf4, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xb0, 0xf, 0xff, 0x20, 0xd, 0xff, 0x50, 0x1,
    0xdf, 0xff, 0xff, 0xb0, 0x0, 0xdf, 0xfa, 0x15,
    0xff, 0xf2, 0x1, 0xdf, 0xff, 0xff, 0xb0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0x71, 0xdf, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x8, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbd,
    0xef, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0x93, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xcf, 0xfd, 0x59, 0xff, 0xf2, 0x3, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xf, 0xff, 0x30, 0xd, 0xff,
    0x50, 0x4, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff,
    0xf4, 0x0, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x40, 0xc, 0xff, 0xd6, 0xaf, 0xff, 0x10,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x40, 0x4f, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xfb, 0x0, 0x7f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xe9, 0x10, 0x0, 0x39, 0xcb,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xf6,
    0xb, 0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xf7, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xf7, 0xc, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf7, 0xc, 0xff, 0xf7, 0x14, 0x44, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xf7, 0x8, 0xbb, 0xba, 0xdf,
    0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xed, 0xdd, 0xdc, 0xff, 0xff, 0xb0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf1, 0x3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x30, 0xff,
    0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x1, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfe, 0x20, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xe2, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xfb, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfe,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfe, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x44, 0xaf, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x67, 0xcf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0x80,

    /* U+F0C9 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x69, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x57, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x74,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x5, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x85, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x30, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x7f,
    0x90, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x11, 0xcf, 0xff, 0xd3, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x4, 0xef,
    0xff, 0xff, 0xf7, 0x3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x9, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x10, 0xaf, 0xff, 0xff, 0xff, 0xa0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x5f, 0xff,
    0xff, 0x50, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x2a, 0xea, 0x20, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4,
    0x0, 0x4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xbd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,

    /* U+F0E7 "" */
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf2, 0x22, 0x22, 0x10, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x11, 0x11, 0x13, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x9d, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x14, 0x55, 0x5b, 0xff, 0xff,
    0x85, 0x55, 0x40, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xd2, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xc0, 0x1f, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfe,
    0xef, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x42, 0x22,
    0x22, 0x21, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x50, 0xcf, 0xff, 0xff, 0xfb,
    0xc, 0x80, 0x0, 0xff, 0xff, 0xff, 0x41, 0xff,
    0xff, 0xff, 0xfb, 0xc, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x41, 0xff, 0xff, 0xff, 0xfb, 0xc, 0xff,
    0x80, 0xff, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff,
    0xfb, 0xc, 0xff, 0xf8, 0xff, 0xff, 0xff, 0x41,
    0xff, 0xff, 0xff, 0xfb, 0x7, 0xaa, 0xa9, 0xff,
    0xff, 0xff, 0x41, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x41, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xee, 0xed, 0xff, 0xff, 0xff,
    0x41, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x41, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x41, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x25, 0x55, 0x55, 0x11,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0x52, 0x2c, 0xf3,
    0x24, 0xfa, 0x22, 0xaf, 0x52, 0x2c, 0xf3, 0x24,
    0xff, 0xf2, 0xff, 0xf2, 0x0, 0xae, 0x0, 0x1f,
    0x80, 0x7, 0xf1, 0x0, 0xae, 0x0, 0xf, 0xff,
    0x2f, 0xff, 0x20, 0xa, 0xe0, 0x1, 0xf8, 0x0,
    0x7f, 0x10, 0xa, 0xe0, 0x0, 0xff, 0xf2, 0xff,
    0xfa, 0x88, 0xef, 0x98, 0xaf, 0xd8, 0x8d, 0xfa,
    0x88, 0xef, 0x98, 0x9f, 0xff, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf9, 0x0,
    0x7f, 0x40, 0x1d, 0xd1, 0x3, 0xfa, 0x0, 0x7f,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0x70, 0x5, 0xf3,
    0x0, 0xcc, 0x0, 0x1f, 0x80, 0x5, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xf8, 0x0, 0x6f, 0x30, 0xc,
    0xd0, 0x2, 0xf9, 0x0, 0x6f, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xfb,
    0x99, 0xef, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0xef, 0xa9, 0xbf, 0xff, 0x2f, 0xff, 0x20, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xe0,
    0x0, 0xff, 0xf2, 0xff, 0xf2, 0x0, 0xae, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xf,
    0xff, 0x2f, 0xff, 0x52, 0x2c, 0xf2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x2c, 0xf2, 0x23, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x33, 0x33, 0x33,
    0x3f, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x5c, 0x10,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xfd, 0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5f, 0xfd, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xfd, 0x10, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x5f, 0xff, 0xfd, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x14,
    0x44, 0x44, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x51, 0x11, 0x11, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x56, 0x78,
    0x87, 0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xfd, 0xa7, 0x66,
    0x67, 0x9c, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xb5, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xaf, 0xff, 0xff, 0xfb, 0x10,
    0xaf, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xd1,
    0xbf, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xe1,
    0xc, 0xfc, 0x10, 0x0, 0x0, 0x1, 0x68, 0x9b,
    0xa8, 0x62, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x20,
    0x0, 0x60, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0x10, 0x0, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfe, 0xa6, 0x43,
    0x45, 0x9d, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9c,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x14, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x40, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F241 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x4, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x20, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F242 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x4, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F243 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x14, 0x44, 0x44, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x4f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F244 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x99,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0,
    0x8f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x8a, 0x71, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x2e, 0xff, 0xfe, 0x20, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0x20, 0x0,
    0xbf, 0xff, 0xff, 0xa1, 0x2d, 0xf4, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0xef, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xef, 0xff, 0xff, 0xea, 0xaa, 0xaa, 0xef, 0xda,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff, 0x80,
    0x8f, 0xff, 0xff, 0x70, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb2, 0x0,
    0x9, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa5, 0x0, 0x0,
    0x0, 0x13, 0x10, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x60, 0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xf6, 0x4e, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0x7e, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x33, 0x33, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x69, 0xcd, 0xdc, 0x95, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf3, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xf2, 0x6, 0xff, 0xff,
    0xfb, 0x0, 0x9, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x8f, 0xff, 0xff, 0x10, 0xe, 0xff, 0xff, 0xff,
    0xf2, 0x2, 0x9, 0xff, 0xff, 0x60, 0x2f, 0xff,
    0xd3, 0xdf, 0xf2, 0xe, 0x20, 0xaf, 0xff, 0xa0,
    0x5f, 0xff, 0x80, 0x1d, 0xf2, 0xf, 0xd0, 0xf,
    0xff, 0xc0, 0x7f, 0xff, 0xf7, 0x1, 0xd2, 0xe,
    0x20, 0xaf, 0xff, 0xe0, 0x9f, 0xff, 0xff, 0x70,
    0x11, 0x2, 0x9, 0xff, 0xff, 0xf0, 0x9f, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xff, 0xff, 0x60, 0x5, 0xff, 0xff,
    0xff, 0xf1, 0xaf, 0xff, 0xff, 0xfe, 0x20, 0x1,
    0xdf, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xf0, 0x8f, 0xff,
    0xfe, 0x20, 0x52, 0x7, 0x2, 0xef, 0xff, 0xf0,
    0x6f, 0xff, 0xe2, 0x5, 0xf2, 0xf, 0x60, 0x3f,
    0xff, 0xe0, 0x4f, 0xff, 0x70, 0x5f, 0xf2, 0xf,
    0xa0, 0x1d, 0xff, 0xc0, 0xf, 0xff, 0xf9, 0xff,
    0xf2, 0xa, 0x1, 0xdf, 0xff, 0x90, 0xc, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x1d, 0xff, 0xff, 0x40,
    0x5, 0xff, 0xff, 0xff, 0xf3, 0x1, 0xdf, 0xff,
    0xfe, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xf3, 0x1d,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xf4, 0xdf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfe, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x4, 0xae, 0xff, 0xff, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x7, 0xbb, 0xbb, 0xbb, 0x50,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x4f, 0xff,
    0xff, 0xff, 0xf3, 0x22, 0x22, 0x20, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0x40, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7, 0xff, 0xfc, 0x7f, 0xff,
    0x9a, 0xff, 0xf6, 0xdf, 0xff, 0x50, 0x7, 0xff,
    0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff,
    0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff,
    0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf,
    0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7,
    0xff, 0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f,
    0xff, 0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24,
    0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7,
    0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50,
    0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0,
    0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf, 0xff,
    0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff,
    0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff,
    0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff,
    0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf,
    0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7,
    0xff, 0xfa, 0x2f, 0xff, 0x57, 0xff, 0xe1, 0xbf,
    0xff, 0x50, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xdb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xc, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0x20, 0xcf, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe2, 0xc,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfe, 0x20, 0xcf, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xe2, 0xc, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xec, 0xa8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xbc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x84,
    0xff, 0xff, 0xfa, 0x3e, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3f, 0xff, 0xa0, 0x2, 0xef, 0xff, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x3, 0xfa, 0x0, 0x1, 0xef, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x20, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xf4,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x1, 0xd6, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0x60, 0x0, 0xdf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x41,
    0xdf, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x10,

    /* U+F7C2 "" */
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xc5, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x3, 0xff, 0x82,
    0x3f, 0x92, 0x3f, 0x92, 0x5f, 0xfe, 0x3, 0xff,
    0xf7, 0x1, 0xf7, 0x1, 0xf7, 0x3, 0xff, 0xe3,
    0xff, 0xff, 0x70, 0x1f, 0x70, 0x1f, 0x70, 0x3f,
    0xfe, 0xff, 0xff, 0xf7, 0x1, 0xf7, 0x1, 0xf7,
    0x3, 0xff, 0xef, 0xff, 0xff, 0x81, 0x2f, 0x81,
    0x2f, 0x81, 0x4f, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x12,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xf1, 0x0, 0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xf1, 0x0, 0x8,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xf1, 0x0, 0x9f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf1,
    0xa, 0xff, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcf, 0xff, 0xf1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x2, 0xdf, 0xff, 0xfe,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x0, 0x0, 0x1d, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 108, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 107, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 36, .adv_w = 156, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 68, .adv_w = 281, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 221, .adv_w = 248, .box_w = 14, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 396, .adv_w = 337, .box_w = 21, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 585, .adv_w = 274, .box_w = 16, .box_h = 19, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 737, .adv_w = 84, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 749, .adv_w = 135, .box_w = 6, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 821, .adv_w = 135, .box_w = 7, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 905, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 955, .adv_w = 233, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1027, .adv_w = 153, .box_w = 8, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 1039, .adv_w = 91, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1047, .adv_w = 141, .box_w = 11, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1185, .adv_w = 267, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1320, .adv_w = 148, .box_w = 7, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1383, .adv_w = 230, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1509, .adv_w = 229, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1635, .adv_w = 268, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1788, .adv_w = 230, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1914, .adv_w = 247, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2040, .adv_w = 239, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2166, .adv_w = 258, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2292, .adv_w = 247, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2427, .adv_w = 91, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2455, .adv_w = 91, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2491, .adv_w = 233, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2563, .adv_w = 233, .box_w = 12, .box_h = 9, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 2617, .adv_w = 233, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2689, .adv_w = 229, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2806, .adv_w = 414, .box_w = 24, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3082, .adv_w = 293, .box_w = 20, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3262, .adv_w = 303, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3406, .adv_w = 289, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3559, .adv_w = 330, .box_w = 18, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3721, .adv_w = 268, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3847, .adv_w = 254, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3964, .adv_w = 309, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4117, .adv_w = 325, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4261, .adv_w = 124, .box_w = 4, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4297, .adv_w = 205, .box_w = 12, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4405, .adv_w = 288, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4549, .adv_w = 238, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4666, .adv_w = 382, .box_w = 20, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4846, .adv_w = 325, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4990, .adv_w = 336, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5161, .adv_w = 289, .box_w = 15, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5296, .adv_w = 336, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5516, .adv_w = 291, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5660, .adv_w = 248, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5786, .adv_w = 235, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5921, .adv_w = 316, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6065, .adv_w = 285, .box_w = 19, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6236, .adv_w = 450, .box_w = 28, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6488, .adv_w = 269, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6641, .adv_w = 259, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6803, .adv_w = 263, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6938, .adv_w = 133, .box_w = 6, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 7010, .adv_w = 141, .box_w = 11, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 7148, .adv_w = 133, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7220, .adv_w = 233, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 7286, .adv_w = 200, .box_w = 13, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7299, .adv_w = 240, .box_w = 8, .box_h = 4, .ofs_x = 2, .ofs_y = 16},
    {.bitmap_index = 7315, .adv_w = 239, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7399, .adv_w = 273, .box_w = 14, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7532, .adv_w = 228, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7623, .adv_w = 273, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7756, .adv_w = 245, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7854, .adv_w = 141, .box_w = 10, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7949, .adv_w = 276, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8082, .adv_w = 272, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8206, .adv_w = 112, .box_w = 5, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8254, .adv_w = 114, .box_w = 9, .box_h = 24, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 8362, .adv_w = 246, .box_w = 14, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8495, .adv_w = 112, .box_w = 3, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8524, .adv_w = 423, .box_w = 23, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8685, .adv_w = 272, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8776, .adv_w = 254, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8874, .adv_w = 273, .box_w = 14, .box_h = 19, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 9007, .adv_w = 273, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9140, .adv_w = 164, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9196, .adv_w = 200, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9280, .adv_w = 166, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9365, .adv_w = 271, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9456, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9561, .adv_w = 360, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9722, .adv_w = 221, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9820, .adv_w = 224, .box_w = 15, .box_h = 19, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 9963, .adv_w = 208, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10047, .adv_w = 140, .box_w = 8, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10143, .adv_w = 120, .box_w = 3, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 10179, .adv_w = 140, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 10275, .adv_w = 233, .box_w = 13, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 10301, .adv_w = 400, .box_w = 26, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 10652, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10890, .adv_w = 400, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11178, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11416, .adv_w = 275, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11578, .adv_w = 400, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11891, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12216, .adv_w = 450, .box_w = 29, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12550, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12875, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13151, .adv_w = 400, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13464, .adv_w = 200, .box_w = 13, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13594, .adv_w = 300, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13784, .adv_w = 450, .box_w = 29, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14147, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14385, .adv_w = 275, .box_w = 18, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14619, .adv_w = 350, .box_w = 16, .box_h = 24, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 14811, .adv_w = 350, .box_w = 22, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15108, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15361, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15614, .adv_w = 350, .box_w = 17, .box_h = 24, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 15818, .adv_w = 350, .box_w = 24, .box_h = 23, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 16094, .adv_w = 250, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 16255, .adv_w = 250, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 16416, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16669, .adv_w = 350, .box_w = 22, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 16724, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17000, .adv_w = 500, .box_w = 33, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 17429, .adv_w = 450, .box_w = 30, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 17819, .adv_w = 400, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18107, .adv_w = 350, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 18261, .adv_w = 350, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 18415, .adv_w = 500, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18719, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18957, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 19282, .adv_w = 400, .box_w = 26, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 19633, .adv_w = 350, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19898, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 20184, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20437, .adv_w = 350, .box_w = 22, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20657, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20895, .adv_w = 250, .box_w = 17, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 21116, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21402, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21688, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21964, .adv_w = 400, .box_w = 27, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 22329, .adv_w = 300, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 22576, .adv_w = 500, .box_w = 32, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 22960, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 23232, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 23504, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 23776, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 24048, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 24320, .adv_w = 500, .box_w = 32, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 24640, .adv_w = 350, .box_w = 20, .box_h = 26, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 24900, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 25186, .adv_w = 400, .box_w = 26, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 25524, .adv_w = 500, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25828, .adv_w = 300, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26075, .adv_w = 402, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 12, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 45, .range_length = 82, .glyph_id_start = 13,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 95,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 0, 13, 14, 15, 16, 17, 18,
    19, 12, 20, 20, 0, 0, 0, 21,
    22, 23, 24, 25, 22, 26, 27, 28,
    29, 29, 30, 31, 32, 29, 29, 22,
    33, 34, 35, 3, 36, 30, 37, 37,
    38, 39, 40, 41, 42, 43, 0, 44,
    0, 45, 46, 47, 48, 49, 50, 51,
    45, 52, 52, 53, 48, 45, 45, 46,
    46, 54, 55, 56, 57, 51, 58, 58,
    59, 58, 60, 41, 0, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 13, 14, 15, 16, 17, 12, 18,
    19, 20, 21, 21, 0, 0, 0, 22,
    23, 24, 25, 23, 25, 25, 25, 23,
    25, 25, 26, 25, 25, 25, 25, 23,
    25, 23, 25, 3, 27, 28, 29, 29,
    30, 31, 32, 33, 34, 35, 0, 36,
    0, 37, 38, 39, 39, 39, 0, 39,
    38, 40, 41, 38, 38, 42, 42, 39,
    42, 39, 42, 43, 44, 45, 46, 46,
    47, 46, 48, 0, 0, 35, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 18, 0, 11, -9, 0, 0, 0,
    0, -22, -24, 3, 19, 9, 7, -16,
    3, 20, 1, 17, 4, 13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 24, 3, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, -8,
    7, 8, 0, 0, -4, 0, -3, 4,
    0, -4, 0, -4, -2, -8, 0, 0,
    0, 0, -4, 0, 0, -5, -6, 0,
    0, -4, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -4, 0,
    0, -11, 0, -48, 0, 0, -8, 0,
    8, 12, 0, 0, -8, 4, 4, 13,
    8, -7, 8, 0, 0, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -20, 0, -16, -3, 0, 0, 0,
    0, 1, 16, 0, -12, -3, -1, 1,
    0, -7, 0, 0, -3, -30, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -32, -3, 15, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 13, 0, 4, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 15, 3, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -15, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 8, 4, 12, -4, 0, 0, 8,
    -4, -13, -55, 3, 11, 8, 1, -5,
    0, 14, 0, 13, 0, 13, 0, -37,
    0, -5, 12, 0, 13, -4, 8, 4,
    0, 0, 1, -4, 0, 0, -7, 32,
    0, 32, 0, 12, 0, 17, 5, 7,
    0, 0, 0, -15, 0, 0, 0, 0,
    1, -3, 0, 3, -7, -5, -8, 3,
    0, -4, 0, 0, 0, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -26, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -22, 0, -25, 0, 0, 0, 0,
    -3, 0, 40, -5, -5, 4, 4, -4,
    0, -5, 4, 0, 0, -21, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -39, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 24, 0, 0, -15, 0, 13, 0,
    -27, -39, -27, -8, 12, 0, 0, -27,
    0, 5, -9, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 12, -49, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 3,
    3, -5, -8, 0, -1, -1, -4, 0,
    0, -3, 0, 0, 0, -8, 0, -3,
    0, -9, -8, 0, -10, -13, -13, -8,
    0, -8, 0, -8, 0, 0, 0, 0,
    -3, 0, 0, 4, 0, 3, -4, 0,
    0, 0, 0, 4, -3, 0, 0, 0,
    -3, 4, 4, -1, 0, 0, 0, -8,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 5, -3, 0, -5, 0, -7, 0,
    0, -3, 0, 12, 0, 0, -4, 0,
    0, 0, 0, 0, -1, 1, -3, -3,
    0, -4, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    -4, -5, 0, 0, 0, 0, 0, 1,
    0, 0, -3, 0, -4, -4, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -3, -5, 0,
    0, -12, -3, -12, 8, 0, 0, -8,
    4, 8, 11, 0, -10, -1, -5, 0,
    -1, -19, 4, -3, 3, -21, 4, 0,
    0, 1, -21, 0, -21, -3, -35, -3,
    0, -20, 0, 8, 11, 0, 5, 0,
    0, 0, 0, 1, 0, -7, -5, 0,
    0, 0, 0, -4, 0, 0, 0, -4,
    0, 0, 0, 0, 0, -2, -2, 0,
    -2, -5, 0, 0, 0, 0, 0, 0,
    0, -4, -4, 0, -3, -5, -3, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -3, 0, -8, 4, 0, 0, -5,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -5, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -6, 0,
    0, 12, -3, 1, -13, 0, 0, 11,
    -20, -21, -17, -8, 4, 0, -3, -26,
    -7, 0, -7, 0, -8, 6, -7, -26,
    0, -11, 0, 0, 2, -1, 3, -3,
    0, 4, 0, -12, -15, 0, -20, -10,
    -8, -10, -12, -5, -11, -1, -8, -11,
    0, 1, 0, -4, 0, 0, 0, 3,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, -2,
    0, -1, -4, 0, -7, -9, -9, -1,
    0, -12, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 2, -2, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 19, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -8, 0, 0, 0,
    0, -20, -12, 0, 0, 0, -6, -20,
    0, 0, -4, 4, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 5, 0,
    3, -8, -8, 0, -4, -4, -5, 0,
    0, 0, 0, 0, 0, -12, 0, -4,
    0, -6, -4, 0, -9, -10, -12, -3,
    0, -8, 0, -12, 0, 0, 0, 0,
    32, 0, 0, 2, 0, 0, -5, 0,
    0, -17, 0, 0, 0, 0, 0, -37,
    -7, 13, 12, -3, -17, 0, 4, -6,
    0, -20, -2, -5, 4, -28, -4, 5,
    0, 6, -14, -6, -15, -13, -17, 0,
    0, -24, 0, 23, 0, 0, -2, 0,
    0, 0, -2, -2, -4, -11, -13, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -2, -4, -6, 0,
    0, -8, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -8, 0, 0, 8,
    -1, 5, 0, -9, 4, -3, -1, -10,
    -4, 0, -5, -4, -3, 0, -6, -7,
    0, 0, -3, -1, -3, -7, -5, 0,
    0, -4, 0, 4, -3, 0, -9, 0,
    0, 0, -8, 0, -7, 0, -7, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 4, 0, -6, 0, -3, -5, -12,
    -3, -3, -3, -1, -3, -5, -1, 0,
    0, 0, 0, 0, -4, -3, -3, 0,
    0, 0, 0, 5, -3, 0, -3, 0,
    0, 0, -3, -5, -3, -4, -5, -4,
    3, 16, -1, 0, -11, 0, -3, 8,
    0, -4, -17, -5, 6, 0, 0, -19,
    -7, 4, -7, 3, 0, -3, -3, -13,
    0, -6, 2, 0, 0, -7, 0, 0,
    0, 4, 4, -8, -8, 0, -7, -4,
    -6, -4, -4, 0, -7, 2, -8, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, -5, 0, 0, -4, -4, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -3, 0,
    0, 0, -6, 0, -8, 0, 0, 0,
    -13, 0, 3, -9, 8, 1, -3, -19,
    0, 0, -9, -4, 0, -16, -10, -11,
    0, 0, -17, -4, -16, -15, -19, 0,
    -10, 0, 3, 27, -5, 0, -9, -4,
    -1, -4, -7, -11, -7, -15, -16, -9,
    0, 0, -3, 0, 1, 0, 0, -28,
    -4, 12, 9, -9, -15, 0, 1, -12,
    0, -20, -3, -4, 8, -37, -5, 1,
    0, 0, -26, -5, -21, -4, -29, 0,
    0, -28, 0, 24, 1, 0, -3, 0,
    0, 0, 0, -2, -3, -15, -3, 0,
    0, 0, 0, 0, -13, 0, -4, 0,
    -1, -11, -19, 0, 0, -2, -6, -12,
    -4, 0, -3, 0, 0, 0, 0, -18,
    -4, -13, -13, -3, -7, -10, -4, -7,
    0, -8, -4, -13, -6, 0, -5, -8,
    -4, -8, 0, 2, 0, -3, -13, 0,
    0, -7, 0, 0, 0, 0, 5, 0,
    3, -8, 16, 0, -4, -4, -5, 0,
    0, 0, 0, 0, 0, -12, 0, -4,
    0, -6, -4, 0, -9, -10, -12, -3,
    0, -8, 3, 16, 0, 0, 0, 0,
    32, 0, 0, 2, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -3, -8,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -4, -4, 0, 0, -8, -4, 0,
    0, -8, 0, 7, -2, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    8, 3, -4, 0, -13, -6, 0, 12,
    -13, -13, -8, -8, 16, 7, 4, -35,
    -3, 8, -4, 0, -4, 4, -4, -14,
    0, -4, 4, -5, -3, -12, -3, 0,
    0, 12, 8, 0, -11, 0, -22, -5,
    12, -5, -15, 1, -5, -13, -13, -4,
    4, 0, -6, 0, -11, 0, 3, 13,
    -9, -15, -16, -10, 12, 0, 1, -29,
    -3, 4, -7, -3, -9, 0, -9, -15,
    -6, -6, -3, 0, 0, -9, -8, -4,
    0, 12, 9, -4, -22, 0, -22, -6,
    0, -14, -23, -1, -13, -7, -13, -11,
    0, 0, -5, 0, -8, -4, 0, -4,
    -7, 0, 7, -13, 4, 0, 0, -21,
    0, -4, -9, -7, -3, -12, -10, -13,
    -9, 0, -12, -4, -9, -8, -12, -4,
    0, 0, 1, 19, -7, 0, -12, -4,
    0, -4, -8, -9, -11, -11, -15, -5,
    8, 0, -6, 0, -20, -5, 2, 8,
    -13, -15, -8, -13, 13, -4, 2, -37,
    -7, 8, -9, -7, -15, 0, -12, -17,
    -5, -4, -3, -4, -8, -12, -1, 0,
    0, 12, 11, -3, -26, 0, -24, -9,
    10, -15, -27, -8, -14, -17, -20, -13,
    0, 0, 0, 0, -5, 0, 0, 4,
    -5, 8, 3, -8, 8, 0, 0, -12,
    -1, 0, -1, 0, 1, 1, -3, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 3, 12, 1, 0, -5, 0,
    0, 0, 0, -3, -3, -5, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 15, 0, 7, 1, 1, -5,
    0, 8, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, 0, -4, 7, 0, 12, 0,
    0, 40, 5, -8, -8, 4, 4, -3,
    1, -20, 0, 0, 19, -24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 15, 56, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, -8, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -11, 0, 0, 1, 0,
    0, 4, 52, -8, -3, 13, 11, -11,
    4, 0, 0, 4, 4, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -52, 11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -11, 0, 0, 0, -11,
    0, 0, 0, 0, -9, -2, 0, 0,
    0, -9, 0, -5, 0, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, -6, 0, -11, 0, 0, 0, -7,
    4, -5, 0, 0, -11, -4, -9, 0,
    0, -11, 0, -4, 0, -19, 0, -4,
    0, 0, -32, -8, -16, -4, -14, 0,
    0, -27, 0, -11, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -6, -7, -3,
    0, 0, 0, 0, -9, 0, -9, 5,
    -4, 8, 0, -3, -9, -3, -7, -8,
    0, -5, -2, -3, 3, -11, -1, 0,
    0, 0, -35, -3, -6, 0, -9, 0,
    -3, -19, -4, 0, 0, -3, -3, 0,
    0, 0, 0, 3, 0, -3, -7, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 0, 0, 0, 0,
    0, -9, 0, -3, 0, 0, 0, -8,
    4, 0, 0, 0, -11, -4, -8, 0,
    0, -11, 0, -4, 0, -19, 0, 0,
    0, 0, -39, 0, -8, -15, -20, 0,
    0, -27, 0, -3, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -6, -2,
    1, 0, 0, 7, -5, 0, 12, 20,
    -4, -4, -12, 5, 20, 7, 9, -11,
    5, 17, 5, 12, 9, 11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 25, 19, -7, -4, 0, -3, 32,
    17, 32, 0, 0, 0, 4, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 6, 0, 0,
    0, 0, -34, -5, -3, -16, -20, 0,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 6, 0, 0,
    0, 0, -34, -5, -3, -16, -20, 0,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -9, 4, 0, -4, 3, 7, 4, -12,
    0, -1, -3, 4, 0, 3, 0, 0,
    0, 0, -10, 0, -4, -3, -8, 0,
    -4, -16, 0, 25, -4, 0, -9, -3,
    0, -3, -7, 0, -4, -11, -8, -5,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 6, 0, 0,
    0, 0, -34, -5, -3, -16, -20, 0,
    0, -27, 0, 0, 0, 0, 0, 0,
    20, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, -13, -5, -4, 12,
    -4, -4, -16, 1, -2, 1, -3, -11,
    1, 9, 1, 3, 1, 3, -10, -16,
    -5, 0, -15, -8, -11, -17, -16, 0,
    -6, -8, -5, -5, -3, -3, -5, -3,
    0, -3, -1, 6, 0, 6, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -4, -4, 0,
    0, -11, 0, -2, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -4, 0,
    0, 0, 0, 0, -3, 0, 0, -7,
    -4, 4, 0, -7, -8, -3, 0, -12,
    -3, -9, -3, -5, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 0, 13, 0, 0, -7, 0,
    0, 0, 0, -5, 0, -4, 0, 0,
    0, 0, -3, 0, -9, 0, 0, 17,
    -5, -13, -12, 3, 4, 4, -1, -11,
    3, 6, 3, 12, 3, 13, -3, -11,
    0, 0, -16, 0, 0, -12, -11, 0,
    0, -8, 0, -5, -7, 0, -6, 0,
    -6, 0, -3, 6, 0, -3, -12, -4,
    0, 0, -4, 0, -8, 0, 0, 5,
    -9, 0, 4, -4, 3, 0, 0, -13,
    0, -3, -1, 0, -4, 4, -3, 0,
    0, 0, -16, -5, -9, 0, -12, 0,
    0, -19, 0, 15, -4, 0, -7, 0,
    2, 0, -4, 0, -4, -12, 0, -4,
    0, 0, 0, 0, -3, 0, 0, 4,
    -5, 1, 0, 0, -5, -3, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 9, 0, 0, -3, 0,
    0, 0, 0, 1, 0, -4, -4, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 3,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserratMedium_25 = {
#else
lv_font_t lv_font_montserratMedium_25 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 25,          /*The maximum line height required by the font  default: (f.src.ascent - f.src.descent)*/
    .base_line = 3,                          /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_25*/

